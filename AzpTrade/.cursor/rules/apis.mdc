---
description: 
globs: 
alwaysApply: false
---
1.获取股票/板块/概念的K线数据(暂时只支持日K)：[yfydata_kInfo.py](mdc:trade/network/yfydata_kInfo.py)

2.获取当前实盘数据，所有个股数据，板块数据，概念数据： [YFYDataMgr.py](mdc:trade/network/YFYDataMgr.py)
    a.all_stock_info_df = stock_data.all_stock_info_df
    b.bidding_stock_df = stock_data.bidding_stock_df
    c.all_board_df = board_concept_data.all_board_df
    d.all_concept_df = board_concept_data.all_concept_df
    e.all_board_stock_df = board_concept_stock.all_board_stock_df
    f.all_concept_stock_df = board_concept_stock.all_concept_stock_df
    g.all_etf_df = pd.DataFrame()
    h.以下是数据的列名：
        --- Stock DF Columns ---
        ['symbol', 'name', 'open', 'close', 'high', 'low', 'turnOverRate', 'openChangePercent', 'changeRate', 'changePercent', 'changePercent5M', 'changePercent3D', 'changePercent5D', 'changePercent10D', 'changePercent20D', 'changePercent30D', 'changePercent60D', 'volume', 'volumeAmount', 'volumeMin', 'boardRank', 'boardMainInFlowAmount', 'boardInFlowRank', 'boardOpen', 'boardClose', 'boardHigh', 'boardLow', 'boardChangePercent', 'boardChangePercentRank', 'boardVolumeAmountRank', 'boardMainInFlowAmountRank', 'totalLimitUpCount', 'totalLimitDownCount', 'isLimitUp', 'isLimitDown', 'ma3', 'ma5', 'ma10', 'ma20', 'ma60', 'ma250', 'ema21', 'ema55', 'ema89', 'ema144', 'hhv3', 'hhv5', 'hhv10', 'hhv20', 'rank', 'mainInFlowAmount', 'mainInFlowRank', 'xlMainInFlowAmount', 'xl_mainInFlowRank', 'kplMainInFlowAmount', 'kpl_mainInFlowRank', 'txMainInFlowAmount', 'txMainInFlowRank', 'lianbanCount', 'conceptChangePercentRank', 'conceptVolumeAmountRank', 'conceptMainInFlowAmountRank', 'conceptName', 'conceptOpen', 'conceptClose', 'conceptHigh', 'conceptLow', 'conceptChangePercent', 'conceptRank', 'conceptMainInFlowAmount', 'conceptInFlowRank', 'conceptSpecialName', 'buy_signal_list', 'sell_signal_list', 'envScore', 'name_hist', 'suspension', 'totalMarketValue', 'circleMarketValue', 'listingDate', 'yes_close', 'yesterdayLimitUp', 'yesterdayVolume', 'yesterdayVolumeAmount', 'volumeMin3D', 'volumeMin5D', 'volume3D', 'volumeAmount3D', 'volume5D', 'volumeAmount5D', 'turnOverRate3D', 'boardName', 'yesterdayRank', 'concept_list', 'rank_ratio', 'rank_diff', 'amount_rank', 'volumeSec3D', 'volumeSec5D', 'volumeSec', 'volume_ratio_3d', 'volume_ratio_5d','changePercent120D','changePercent250D']
        个股历史数据：非当天实盘的，会通过symbol合并进 all_stock_info_df 里面
        ['symbol', 'name', 'yes_close', 'circleMarketValue', 'yesterdayRank', 'turnOverRate3D','boardName', 'suspension', "totalMarketValue", "concept_list","yesterdayVolume", "yesterdayVolumeAmount",'volumeMin5D', 'volumeMin3D', "volume3D", "volumeAmount3D", "volume5D", "volumeAmount5D", "listingDate", "yesterdayLimitUp"]

        --- Board DF Columns ---
        ['symbol', 'name', 'latestPrice', 'volume', 'amount', 'changePercent', 'open', 'close', 'high', 'low', 'changePercent2D', 'changePercent3D', 'changePercent5D', 'changePercent10D', 'changePercent30D', 'totalMarketValue', 'turnOverRate', 'volume5D', 'upStockCount', 'downStockCount', 'upFirstStockSymbol', 'upFirstStockName', 'upFirstChangePercent', 'upFirstCalcStockSymbol', 'upFirstCalcStockName', 'upFirstCalcChangePercent', 'volumeAmountFirstStockSymbol', 'volumeAmountFirstStockName', 'volumeAmountFirstChangePercent', 'mainInFlowFirstStockSymbol', 'mainInFlowFirstStockName', 'mainInFlowFirstChangePercent', 'downFirstStockSymbol', 'downFirstStockName', 'downFirstChangePercent', 'mainInFlowAmount', 'mainInFlowRank', 'rank', 'rank3D', 'support', 'pressure', 'isSpecialChange', 'stockLimitUpCount', 'upStockPercent', 'buy_signal_list', 'sell_signal_list','changePercent60D', 'changePercent120D', 'changePercent250D']

        --- Concept DF Columns ---
        ['symbol', 'name', 'latestPrice', 'volume', 'amount', 'changePercent', 'open', 'close', 'high', 'low', 'changePercent2D', 'changePercent3D', 'changePercent5D', 'changePercent10D', 'changePercent30D', 'totalMarketValue', 'turnOverRate', 'volume5D', 'upStockCount', 'downStockCount', 'upFirstStockSymbol', 'upFirstStockName', 'upFirstChangePercent', 'upFirstCalcStockSymbol', 'upFirstCalcStockName', 'upFirstCalcChangePercent', 'volumeAmountFirstStockSymbol', 'volumeAmountFirstStockName', 'volumeAmountFirstChangePercent', 'mainInFlowFirstStockSymbol', 'mainInFlowFirstStockName', 'mainInFlowFirstChangePercent', 'downFirstStockSymbol', 'downFirstStockName', 'downFirstChangePercent', 'mainInFlowAmount', 'mainInFlowRank', 'rank', 'rank3D', 'support', 'pressure', 'isSpecialChange', 'stockLimitUpCount', 'upStockPercent', 'buy_signal_list', 'sell_signal_list','changePercent60D', 'changePercent120D', 'changePercent250D']
    

3.获取各个指数K线数据：[MarketIndexKinfoMgr.py](mdc:trade/manager/MarketIndexKinfoMgr.py)
    a.注意symbol，需要有 1.000001，或者 0.399006等多个格式
    b.同时要使用代理 [KdlProxyMgr.py](mdc:trade/manager/KdlProxyMgr.py)

4.redis的存储：[StockRedisMgr.py](mdc:trade/redis/StockRedisMgr.py)

5.涨停/炸板/跌停股票的接口： [ZTStockManager.py](mdc:trade/html/ZTStockManager.py)

6.通过AKShare获取个股数据：
    # 获取股票基本信息
    stock_info = ak.stock_individual_info_em(symbol=stock_code)
    # 获取关键指标
    financial_abstract = ak.stock_financial_abstract(symbol=stock_code)

7.获取账户持仓数据以及买卖： [YFYAccountMgr.py](mdc:trade/network/YFYAccountMgr.py)
    //#Java的返回数据格式如下：
    String account; // 资金账号
    Bigdecimal totalCash; // 总资金
    Bigdecimal availableCash; // 可用资金
    Bigdecimal frozenCash; // 冻结资金
    Bigdecimal marketValue; // 总持仓市值
    int positionCount; // 持仓个数
    Bigdecimal totalProfit; // 总盈利
    BigDecimal totalTodayProfit; // 当日总盈亏
    List<PositionInfo> positionInfoGroup; //持仓列表详情
    PositionInfo由以下组成
    {
        String symbol； // 股票代码
        String name； // 股票代码
        Bigdecimal changePercent； // 涨跌幅
        Bigdecimal latestPrice； // 当前价
        Bigdecimal tradePrice； // 交易价格
        Bigdecimal tradeAmount； // 交易数量
        Bigdecimal frozenAmount； // 冻结数量
        Bigdecimal availableAmount； // 可用数量
        Bigdecimal positionGold； // 持仓金额
        Bigdecimal profit； // 盈亏金额
        String strategyInfo； // 买策略信息，用逗号分隔
        String boardName；//板块名
        Bigdecimal boardChangePercent;// 板块涨跌幅
        String boardFirstUpName；//板块第一名？
        String conceptName；//概念名
        Bigdecimal conceptChangePercent；//概念涨跌幅
        String conceptFirstUpName；//概念第一名？
        String tradeBuyTime；//第一次开仓交易时间，时间字符串
        String latestBuyTradeTime；//最近一次补仓交易时间，时间字符串
        String sellTag; // 卖出标签
        BigDecimal todayProfit; // 当日盈亏
        BigDecimal todayProfitPercent; // 当日盈亏比例
        BigDecimal positionProfit; // 持仓盈亏
        BigDecimal positionProfitPercent; // 持仓盈亏比例
    }

8.ETF数据获取接口： [ETFAsyncDataMgr.py](mdc:trade/manager/ETFAsyncDataMgr.py)


end.日志系统： [YfyLog.py](mdc:trade/log/YfyLog.py)


