m4trace:configure.in:6: -1- AC_INIT([ta-lib], [0.4.0], [http://sourceforge.net/tracker/?group_id=8903&atid=108903])
m4trace:configure.in:6: -1- m4_pattern_forbid([^_?A[CHUM]_])
m4trace:configure.in:6: -1- m4_pattern_forbid([_AC_])
m4trace:configure.in:6: -1- m4_pattern_forbid([^LIBOBJS$], [do not use LIBOBJS directly, use AC_LIBOBJ (see section `AC_LIBOBJ vs LIBOBJS'])
m4trace:configure.in:6: -1- m4_pattern_allow([^AS_FLAGS$])
m4trace:configure.in:6: -1- m4_pattern_forbid([^_?m4_])
m4trace:configure.in:6: -1- m4_pattern_forbid([^dnl$])
m4trace:configure.in:6: -1- m4_pattern_forbid([^_?AS_])
m4trace:configure.in:6: -1- AC_SUBST([SHELL], [${CONFIG_SHELL-/bin/sh}])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([SHELL])
m4trace:configure.in:6: -1- m4_pattern_allow([^SHELL$])
m4trace:configure.in:6: -1- AC_SUBST([PATH_SEPARATOR])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([PATH_SEPARATOR])
m4trace:configure.in:6: -1- m4_pattern_allow([^PATH_SEPARATOR$])
m4trace:configure.in:6: -1- AC_SUBST([PACKAGE_NAME], [m4_ifdef([AC_PACKAGE_NAME],      ['AC_PACKAGE_NAME'])])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([PACKAGE_NAME])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.in:6: -1- AC_SUBST([PACKAGE_TARNAME], [m4_ifdef([AC_PACKAGE_TARNAME],   ['AC_PACKAGE_TARNAME'])])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([PACKAGE_TARNAME])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.in:6: -1- AC_SUBST([PACKAGE_VERSION], [m4_ifdef([AC_PACKAGE_VERSION],   ['AC_PACKAGE_VERSION'])])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([PACKAGE_VERSION])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.in:6: -1- AC_SUBST([PACKAGE_STRING], [m4_ifdef([AC_PACKAGE_STRING],    ['AC_PACKAGE_STRING'])])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([PACKAGE_STRING])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.in:6: -1- AC_SUBST([PACKAGE_BUGREPORT], [m4_ifdef([AC_PACKAGE_BUGREPORT], ['AC_PACKAGE_BUGREPORT'])])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([PACKAGE_BUGREPORT])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.in:6: -1- AC_SUBST([exec_prefix], [NONE])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([exec_prefix])
m4trace:configure.in:6: -1- m4_pattern_allow([^exec_prefix$])
m4trace:configure.in:6: -1- AC_SUBST([prefix], [NONE])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([prefix])
m4trace:configure.in:6: -1- m4_pattern_allow([^prefix$])
m4trace:configure.in:6: -1- AC_SUBST([program_transform_name], [s,x,x,])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([program_transform_name])
m4trace:configure.in:6: -1- m4_pattern_allow([^program_transform_name$])
m4trace:configure.in:6: -1- AC_SUBST([bindir], ['${exec_prefix}/bin'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([bindir])
m4trace:configure.in:6: -1- m4_pattern_allow([^bindir$])
m4trace:configure.in:6: -1- AC_SUBST([sbindir], ['${exec_prefix}/sbin'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([sbindir])
m4trace:configure.in:6: -1- m4_pattern_allow([^sbindir$])
m4trace:configure.in:6: -1- AC_SUBST([libexecdir], ['${exec_prefix}/libexec'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([libexecdir])
m4trace:configure.in:6: -1- m4_pattern_allow([^libexecdir$])
m4trace:configure.in:6: -1- AC_SUBST([datarootdir], ['${prefix}/share'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([datarootdir])
m4trace:configure.in:6: -1- m4_pattern_allow([^datarootdir$])
m4trace:configure.in:6: -1- AC_SUBST([datadir], ['${datarootdir}'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([datadir])
m4trace:configure.in:6: -1- m4_pattern_allow([^datadir$])
m4trace:configure.in:6: -1- AC_SUBST([sysconfdir], ['${prefix}/etc'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([sysconfdir])
m4trace:configure.in:6: -1- m4_pattern_allow([^sysconfdir$])
m4trace:configure.in:6: -1- AC_SUBST([sharedstatedir], ['${prefix}/com'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([sharedstatedir])
m4trace:configure.in:6: -1- m4_pattern_allow([^sharedstatedir$])
m4trace:configure.in:6: -1- AC_SUBST([localstatedir], ['${prefix}/var'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([localstatedir])
m4trace:configure.in:6: -1- m4_pattern_allow([^localstatedir$])
m4trace:configure.in:6: -1- AC_SUBST([includedir], ['${prefix}/include'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([includedir])
m4trace:configure.in:6: -1- m4_pattern_allow([^includedir$])
m4trace:configure.in:6: -1- AC_SUBST([oldincludedir], ['/usr/include'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([oldincludedir])
m4trace:configure.in:6: -1- m4_pattern_allow([^oldincludedir$])
m4trace:configure.in:6: -1- AC_SUBST([docdir], [m4_ifset([AC_PACKAGE_TARNAME],
				     ['${datarootdir}/doc/${PACKAGE_TARNAME}'],
				     ['${datarootdir}/doc/${PACKAGE}'])])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([docdir])
m4trace:configure.in:6: -1- m4_pattern_allow([^docdir$])
m4trace:configure.in:6: -1- AC_SUBST([infodir], ['${datarootdir}/info'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([infodir])
m4trace:configure.in:6: -1- m4_pattern_allow([^infodir$])
m4trace:configure.in:6: -1- AC_SUBST([htmldir], ['${docdir}'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([htmldir])
m4trace:configure.in:6: -1- m4_pattern_allow([^htmldir$])
m4trace:configure.in:6: -1- AC_SUBST([dvidir], ['${docdir}'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([dvidir])
m4trace:configure.in:6: -1- m4_pattern_allow([^dvidir$])
m4trace:configure.in:6: -1- AC_SUBST([pdfdir], ['${docdir}'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([pdfdir])
m4trace:configure.in:6: -1- m4_pattern_allow([^pdfdir$])
m4trace:configure.in:6: -1- AC_SUBST([psdir], ['${docdir}'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([psdir])
m4trace:configure.in:6: -1- m4_pattern_allow([^psdir$])
m4trace:configure.in:6: -1- AC_SUBST([libdir], ['${exec_prefix}/lib'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([libdir])
m4trace:configure.in:6: -1- m4_pattern_allow([^libdir$])
m4trace:configure.in:6: -1- AC_SUBST([localedir], ['${datarootdir}/locale'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([localedir])
m4trace:configure.in:6: -1- m4_pattern_allow([^localedir$])
m4trace:configure.in:6: -1- AC_SUBST([mandir], ['${datarootdir}/man'])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([mandir])
m4trace:configure.in:6: -1- m4_pattern_allow([^mandir$])
m4trace:configure.in:6: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_NAME])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_NAME$])
m4trace:configure.in:6: -1- AH_OUTPUT([PACKAGE_NAME], [/* Define to the full name of this package. */
#undef PACKAGE_NAME])
m4trace:configure.in:6: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_TARNAME])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_TARNAME$])
m4trace:configure.in:6: -1- AH_OUTPUT([PACKAGE_TARNAME], [/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME])
m4trace:configure.in:6: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_VERSION])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_VERSION$])
m4trace:configure.in:6: -1- AH_OUTPUT([PACKAGE_VERSION], [/* Define to the version of this package. */
#undef PACKAGE_VERSION])
m4trace:configure.in:6: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_STRING])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_STRING$])
m4trace:configure.in:6: -1- AH_OUTPUT([PACKAGE_STRING], [/* Define to the full name and version of this package. */
#undef PACKAGE_STRING])
m4trace:configure.in:6: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE_BUGREPORT])
m4trace:configure.in:6: -1- m4_pattern_allow([^PACKAGE_BUGREPORT$])
m4trace:configure.in:6: -1- AH_OUTPUT([PACKAGE_BUGREPORT], [/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT])
m4trace:configure.in:6: -1- AC_SUBST([DEFS])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([DEFS])
m4trace:configure.in:6: -1- m4_pattern_allow([^DEFS$])
m4trace:configure.in:6: -1- AC_SUBST([ECHO_C])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([ECHO_C])
m4trace:configure.in:6: -1- m4_pattern_allow([^ECHO_C$])
m4trace:configure.in:6: -1- AC_SUBST([ECHO_N])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([ECHO_N])
m4trace:configure.in:6: -1- m4_pattern_allow([^ECHO_N$])
m4trace:configure.in:6: -1- AC_SUBST([ECHO_T])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([ECHO_T])
m4trace:configure.in:6: -1- m4_pattern_allow([^ECHO_T$])
m4trace:configure.in:6: -1- AC_SUBST([LIBS])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.in:6: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.in:6: -1- AC_SUBST([build_alias])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([build_alias])
m4trace:configure.in:6: -1- m4_pattern_allow([^build_alias$])
m4trace:configure.in:6: -1- AC_SUBST([host_alias])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([host_alias])
m4trace:configure.in:6: -1- m4_pattern_allow([^host_alias$])
m4trace:configure.in:6: -1- AC_SUBST([target_alias])
m4trace:configure.in:6: -1- AC_SUBST_TRACE([target_alias])
m4trace:configure.in:6: -1- m4_pattern_allow([^target_alias$])
m4trace:configure.in:8: -1- AC_CONFIG_HEADERS([include/ta_config.h])
m4trace:configure.in:9: -1- AM_INIT_AUTOMAKE([ta-lib], [0.4.0])
m4trace:configure.in:9: -1- m4_pattern_allow([^AM_[A-Z]+FLAGS$])
m4trace:configure.in:9: -1- AM_AUTOMAKE_VERSION([1.10])
m4trace:configure.in:9: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.in:9: -1- AC_SUBST([INSTALL_PROGRAM])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([INSTALL_PROGRAM])
m4trace:configure.in:9: -1- m4_pattern_allow([^INSTALL_PROGRAM$])
m4trace:configure.in:9: -1- AC_SUBST([INSTALL_SCRIPT])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([INSTALL_SCRIPT])
m4trace:configure.in:9: -1- m4_pattern_allow([^INSTALL_SCRIPT$])
m4trace:configure.in:9: -1- AC_SUBST([INSTALL_DATA])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([INSTALL_DATA])
m4trace:configure.in:9: -1- m4_pattern_allow([^INSTALL_DATA$])
m4trace:configure.in:9: -1- AC_SUBST([am__isrc], [' -I$(srcdir)'])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([am__isrc])
m4trace:configure.in:9: -1- m4_pattern_allow([^am__isrc$])
m4trace:configure.in:9: -1- _AM_SUBST_NOTMAKE([am__isrc])
m4trace:configure.in:9: -1- AC_SUBST([CYGPATH_W])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([CYGPATH_W])
m4trace:configure.in:9: -1- m4_pattern_allow([^CYGPATH_W$])
m4trace:configure.in:9: -1- AC_SUBST([PACKAGE], [ta-lib])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([PACKAGE])
m4trace:configure.in:9: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.in:9: -1- AC_SUBST([VERSION], [0.4.0])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([VERSION])
m4trace:configure.in:9: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.in:9: -1- AC_DEFINE_TRACE_LITERAL([PACKAGE])
m4trace:configure.in:9: -1- m4_pattern_allow([^PACKAGE$])
m4trace:configure.in:9: -1- AH_OUTPUT([PACKAGE], [/* Name of package */
#undef PACKAGE])
m4trace:configure.in:9: -1- AC_DEFINE_TRACE_LITERAL([VERSION])
m4trace:configure.in:9: -1- m4_pattern_allow([^VERSION$])
m4trace:configure.in:9: -1- AH_OUTPUT([VERSION], [/* Version number of package */
#undef VERSION])
m4trace:configure.in:9: -1- AC_REQUIRE_AUX_FILE([missing])
m4trace:configure.in:9: -1- AC_SUBST([ACLOCAL])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([ACLOCAL])
m4trace:configure.in:9: -1- m4_pattern_allow([^ACLOCAL$])
m4trace:configure.in:9: -1- AC_SUBST([AUTOCONF])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([AUTOCONF])
m4trace:configure.in:9: -1- m4_pattern_allow([^AUTOCONF$])
m4trace:configure.in:9: -1- AC_SUBST([AUTOMAKE])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([AUTOMAKE])
m4trace:configure.in:9: -1- m4_pattern_allow([^AUTOMAKE$])
m4trace:configure.in:9: -1- AC_SUBST([AUTOHEADER])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([AUTOHEADER])
m4trace:configure.in:9: -1- m4_pattern_allow([^AUTOHEADER$])
m4trace:configure.in:9: -1- AC_SUBST([MAKEINFO])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([MAKEINFO])
m4trace:configure.in:9: -1- m4_pattern_allow([^MAKEINFO$])
m4trace:configure.in:9: -1- AC_SUBST([install_sh])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([install_sh])
m4trace:configure.in:9: -1- m4_pattern_allow([^install_sh$])
m4trace:configure.in:9: -1- AC_SUBST([STRIP])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.in:9: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.in:9: -1- AC_SUBST([INSTALL_STRIP_PROGRAM])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([INSTALL_STRIP_PROGRAM])
m4trace:configure.in:9: -1- m4_pattern_allow([^INSTALL_STRIP_PROGRAM$])
m4trace:configure.in:9: -1- AC_REQUIRE_AUX_FILE([install-sh])
m4trace:configure.in:9: -1- AC_SUBST([mkdir_p], ["$MKDIR_P"])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([mkdir_p])
m4trace:configure.in:9: -1- m4_pattern_allow([^mkdir_p$])
m4trace:configure.in:9: -1- AC_SUBST([AWK])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([AWK])
m4trace:configure.in:9: -1- m4_pattern_allow([^AWK$])
m4trace:configure.in:9: -1- AC_SUBST([SET_MAKE])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([SET_MAKE])
m4trace:configure.in:9: -1- m4_pattern_allow([^SET_MAKE$])
m4trace:configure.in:9: -1- AC_SUBST([am__leading_dot])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([am__leading_dot])
m4trace:configure.in:9: -1- m4_pattern_allow([^am__leading_dot$])
m4trace:configure.in:9: -1- AC_SUBST([AMTAR])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([AMTAR])
m4trace:configure.in:9: -1- m4_pattern_allow([^AMTAR$])
m4trace:configure.in:9: -1- AC_SUBST([am__tar])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([am__tar])
m4trace:configure.in:9: -1- m4_pattern_allow([^am__tar$])
m4trace:configure.in:9: -1- AC_SUBST([am__untar])
m4trace:configure.in:9: -1- AC_SUBST_TRACE([am__untar])
m4trace:configure.in:9: -1- m4_pattern_allow([^am__untar$])
m4trace:configure.in:12: -1- AC_SUBST([CC])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.in:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.in:12: -1- AC_SUBST([CFLAGS])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([CFLAGS])
m4trace:configure.in:12: -1- m4_pattern_allow([^CFLAGS$])
m4trace:configure.in:12: -1- AC_SUBST([LDFLAGS])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.in:12: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.in:12: -1- AC_SUBST([LIBS])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.in:12: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.in:12: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.in:12: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.in:12: -1- AC_SUBST([CC])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.in:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.in:12: -1- AC_SUBST([CC])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.in:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.in:12: -1- AC_SUBST([CC])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.in:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.in:12: -1- AC_SUBST([CC])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([CC])
m4trace:configure.in:12: -1- m4_pattern_allow([^CC$])
m4trace:configure.in:12: -1- AC_SUBST([ac_ct_CC])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([ac_ct_CC])
m4trace:configure.in:12: -1- m4_pattern_allow([^ac_ct_CC$])
m4trace:configure.in:12: -1- AC_SUBST([EXEEXT], [$ac_cv_exeext])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([EXEEXT])
m4trace:configure.in:12: -1- m4_pattern_allow([^EXEEXT$])
m4trace:configure.in:12: -1- AC_SUBST([OBJEXT], [$ac_cv_objext])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([OBJEXT])
m4trace:configure.in:12: -1- m4_pattern_allow([^OBJEXT$])
m4trace:configure.in:12: -1- AC_SUBST([DEPDIR], ["${am__leading_dot}deps"])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([DEPDIR])
m4trace:configure.in:12: -1- m4_pattern_allow([^DEPDIR$])
m4trace:configure.in:12: -1- AC_SUBST([am__include])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([am__include])
m4trace:configure.in:12: -1- m4_pattern_allow([^am__include$])
m4trace:configure.in:12: -1- AC_SUBST([am__quote])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([am__quote])
m4trace:configure.in:12: -1- m4_pattern_allow([^am__quote$])
m4trace:configure.in:12: -1- AM_CONDITIONAL([AMDEP], [test "x$enable_dependency_tracking" != xno])
m4trace:configure.in:12: -1- AC_SUBST([AMDEP_TRUE])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([AMDEP_TRUE])
m4trace:configure.in:12: -1- m4_pattern_allow([^AMDEP_TRUE$])
m4trace:configure.in:12: -1- AC_SUBST([AMDEP_FALSE])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([AMDEP_FALSE])
m4trace:configure.in:12: -1- m4_pattern_allow([^AMDEP_FALSE$])
m4trace:configure.in:12: -1- _AM_SUBST_NOTMAKE([AMDEP_TRUE])
m4trace:configure.in:12: -1- _AM_SUBST_NOTMAKE([AMDEP_FALSE])
m4trace:configure.in:12: -1- AC_SUBST([AMDEPBACKSLASH])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([AMDEPBACKSLASH])
m4trace:configure.in:12: -1- m4_pattern_allow([^AMDEPBACKSLASH$])
m4trace:configure.in:12: -1- _AM_SUBST_NOTMAKE([AMDEPBACKSLASH])
m4trace:configure.in:12: -1- AC_SUBST([CCDEPMODE], [depmode=$am_cv_CC_dependencies_compiler_type])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([CCDEPMODE])
m4trace:configure.in:12: -1- m4_pattern_allow([^CCDEPMODE$])
m4trace:configure.in:12: -1- AM_CONDITIONAL([am__fastdepCC], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CC_dependencies_compiler_type" = gcc3])
m4trace:configure.in:12: -1- AC_SUBST([am__fastdepCC_TRUE])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([am__fastdepCC_TRUE])
m4trace:configure.in:12: -1- m4_pattern_allow([^am__fastdepCC_TRUE$])
m4trace:configure.in:12: -1- AC_SUBST([am__fastdepCC_FALSE])
m4trace:configure.in:12: -1- AC_SUBST_TRACE([am__fastdepCC_FALSE])
m4trace:configure.in:12: -1- m4_pattern_allow([^am__fastdepCC_FALSE$])
m4trace:configure.in:12: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_TRUE])
m4trace:configure.in:12: -1- _AM_SUBST_NOTMAKE([am__fastdepCC_FALSE])
m4trace:configure.in:13: -1- AC_PROG_LIBTOOL
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:209: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1364: AC_ARG_ENABLE is expanded from...
aclocal.m4:1908: AC_ENABLE_SHARED is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:209: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1364: AC_ARG_ENABLE is expanded from...
aclocal.m4:1947: AC_ENABLE_STATIC is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:209: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1364: AC_ARG_ENABLE is expanded from...
aclocal.m4:1986: AC_ENABLE_FAST_INSTALL is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- AC_CANONICAL_HOST
m4trace:configure.in:13: -1- AC_CANONICAL_BUILD
m4trace:configure.in:13: -1- AC_REQUIRE_AUX_FILE([config.sub])
m4trace:configure.in:13: -1- AC_REQUIRE_AUX_FILE([config.guess])
m4trace:configure.in:13: -1- AC_SUBST([build], [$ac_cv_build])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([build])
m4trace:configure.in:13: -1- m4_pattern_allow([^build$])
m4trace:configure.in:13: -1- AC_SUBST([build_cpu], [$[1]])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([build_cpu])
m4trace:configure.in:13: -1- m4_pattern_allow([^build_cpu$])
m4trace:configure.in:13: -1- AC_SUBST([build_vendor], [$[2]])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([build_vendor])
m4trace:configure.in:13: -1- m4_pattern_allow([^build_vendor$])
m4trace:configure.in:13: -1- AC_SUBST([build_os])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([build_os])
m4trace:configure.in:13: -1- m4_pattern_allow([^build_os$])
m4trace:configure.in:13: -1- AC_SUBST([host], [$ac_cv_host])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([host])
m4trace:configure.in:13: -1- m4_pattern_allow([^host$])
m4trace:configure.in:13: -1- AC_SUBST([host_cpu], [$[1]])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([host_cpu])
m4trace:configure.in:13: -1- m4_pattern_allow([^host_cpu$])
m4trace:configure.in:13: -1- AC_SUBST([host_vendor], [$[2]])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([host_vendor])
m4trace:configure.in:13: -1- m4_pattern_allow([^host_vendor$])
m4trace:configure.in:13: -1- AC_SUBST([host_os])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([host_os])
m4trace:configure.in:13: -1- m4_pattern_allow([^host_os$])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:209: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1382: AC_ARG_WITH is expanded from...
aclocal.m4:2127: AC_PROG_LD is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- AC_SUBST([GREP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.in:13: -1- m4_pattern_allow([^GREP$])
m4trace:configure.in:13: -1- AC_SUBST([GREP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([GREP])
m4trace:configure.in:13: -1- m4_pattern_allow([^GREP$])
m4trace:configure.in:13: -1- AC_SUBST([EGREP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([EGREP])
m4trace:configure.in:13: -1- m4_pattern_allow([^EGREP$])
m4trace:configure.in:13: -1- AC_SUBST([EGREP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([EGREP])
m4trace:configure.in:13: -1- m4_pattern_allow([^EGREP$])
m4trace:configure.in:13: -1- AC_SUBST([LN_S], [$as_ln_s])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([LN_S])
m4trace:configure.in:13: -1- m4_pattern_allow([^LN_S$])
m4trace:configure.in:13: -1- AC_SUBST([ECHO])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([ECHO])
m4trace:configure.in:13: -1- m4_pattern_allow([^ECHO$])
m4trace:configure.in:13: -1- AC_SUBST([AR])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([AR])
m4trace:configure.in:13: -1- m4_pattern_allow([^AR$])
m4trace:configure.in:13: -1- AC_SUBST([RANLIB])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([RANLIB])
m4trace:configure.in:13: -1- m4_pattern_allow([^RANLIB$])
m4trace:configure.in:13: -1- AC_SUBST([STRIP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([STRIP])
m4trace:configure.in:13: -1- m4_pattern_allow([^STRIP$])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:209: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1364: AC_ARG_ENABLE is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:209: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1382: AC_ARG_WITH is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_TRY_LINK' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:2414: AC_TRY_LINK is expanded from...
../../lib/m4sugar/m4sh.m4:516: AS_IF is expanded from...
../../lib/autoconf/general.m4:1898: AC_CACHE_VAL is expanded from...
../../lib/autoconf/general.m4:1911: AC_CACHE_CHECK is expanded from...
aclocal.m4:480: _LT_AC_LOCK is expanded from...
aclocal.m4:1084: AC_LIBTOOL_SYS_HARD_LINK_LOCKS is expanded from...
aclocal.m4:2650: _LT_AC_LANG_C_CONFIG is expanded from...
aclocal.m4:2649: AC_LIBTOOL_LANG_C_CONFIG is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_DLFCN_H], [/* Define to 1 if you have the <dlfcn.h> header file. */
#undef HAVE_DLFCN_H])
m4trace:configure.in:13: -1- AC_SUBST([CPP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.in:13: -1- m4_pattern_allow([^CPP$])
m4trace:configure.in:13: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.in:13: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.in:13: -1- AC_SUBST([CPP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CPP])
m4trace:configure.in:13: -1- m4_pattern_allow([^CPP$])
m4trace:configure.in:13: -1- AC_DEFINE_TRACE_LITERAL([STDC_HEADERS])
m4trace:configure.in:13: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.in:13: -1- AH_OUTPUT([STDC_HEADERS], [/* Define to 1 if you have the ANSI C header files. */
#undef STDC_HEADERS])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_SYS_TYPES_H], [/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_SYS_STAT_H], [/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_MEMORY_H], [/* Define to 1 if you have the <memory.h> header file. */
#undef HAVE_MEMORY_H])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_STRINGS_H], [/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_INTTYPES_H], [/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H])
m4trace:configure.in:13: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H])
m4trace:configure.in:13: -1- _LT_AC_TAGCONFIG
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_HELP_STRING' is obsolete.
You should run autoupdate.], [../../lib/autoconf/general.m4:209: AC_HELP_STRING is expanded from...
../../lib/autoconf/general.m4:1382: AC_ARG_WITH is expanded from...
aclocal.m4:1786: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [back quotes and double quotes must not be escaped in: $as_me:$LINENO: error: tag name \"$tagname\" already exists], [aclocal.m4:1786: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [back quotes and double quotes must not be escaped in: $as_me: error: tag name \"$tagname\" already exists], [aclocal.m4:1786: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- AC_SUBST([CXX])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CXX])
m4trace:configure.in:13: -1- m4_pattern_allow([^CXX$])
m4trace:configure.in:13: -1- AC_SUBST([CXXFLAGS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CXXFLAGS])
m4trace:configure.in:13: -1- m4_pattern_allow([^CXXFLAGS$])
m4trace:configure.in:13: -1- AC_SUBST([LDFLAGS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.in:13: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.in:13: -1- AC_SUBST([LIBS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.in:13: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.in:13: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.in:13: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.in:13: -1- AC_SUBST([CXX])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CXX])
m4trace:configure.in:13: -1- m4_pattern_allow([^CXX$])
m4trace:configure.in:13: -1- AC_SUBST([ac_ct_CXX])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([ac_ct_CXX])
m4trace:configure.in:13: -1- m4_pattern_allow([^ac_ct_CXX$])
m4trace:configure.in:13: -1- AC_SUBST([CXXDEPMODE], [depmode=$am_cv_CXX_dependencies_compiler_type])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CXXDEPMODE])
m4trace:configure.in:13: -1- m4_pattern_allow([^CXXDEPMODE$])
m4trace:configure.in:13: -1- AM_CONDITIONAL([am__fastdepCXX], [
  test "x$enable_dependency_tracking" != xno \
  && test "$am_cv_CXX_dependencies_compiler_type" = gcc3])
m4trace:configure.in:13: -1- AC_SUBST([am__fastdepCXX_TRUE])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([am__fastdepCXX_TRUE])
m4trace:configure.in:13: -1- m4_pattern_allow([^am__fastdepCXX_TRUE$])
m4trace:configure.in:13: -1- AC_SUBST([am__fastdepCXX_FALSE])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([am__fastdepCXX_FALSE])
m4trace:configure.in:13: -1- m4_pattern_allow([^am__fastdepCXX_FALSE$])
m4trace:configure.in:13: -1- _AM_SUBST_NOTMAKE([am__fastdepCXX_TRUE])
m4trace:configure.in:13: -1- _AM_SUBST_NOTMAKE([am__fastdepCXX_FALSE])
m4trace:configure.in:13: -1- AC_SUBST([CXXCPP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CXXCPP])
m4trace:configure.in:13: -1- m4_pattern_allow([^CXXCPP$])
m4trace:configure.in:13: -1- AC_SUBST([CPPFLAGS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CPPFLAGS])
m4trace:configure.in:13: -1- m4_pattern_allow([^CPPFLAGS$])
m4trace:configure.in:13: -1- AC_SUBST([CXXCPP])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([CXXCPP])
m4trace:configure.in:13: -1- m4_pattern_allow([^CXXCPP$])
m4trace:configure.in:13: -1- AC_SUBST([F77])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([F77])
m4trace:configure.in:13: -1- m4_pattern_allow([^F77$])
m4trace:configure.in:13: -1- AC_SUBST([FFLAGS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([FFLAGS])
m4trace:configure.in:13: -1- m4_pattern_allow([^FFLAGS$])
m4trace:configure.in:13: -1- AC_SUBST([LDFLAGS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([LDFLAGS])
m4trace:configure.in:13: -1- m4_pattern_allow([^LDFLAGS$])
m4trace:configure.in:13: -1- AC_SUBST([LIBS])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([LIBS])
m4trace:configure.in:13: -1- m4_pattern_allow([^LIBS$])
m4trace:configure.in:13: -1- AC_SUBST([F77])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([F77])
m4trace:configure.in:13: -1- m4_pattern_allow([^F77$])
m4trace:configure.in:13: -1- AC_SUBST([ac_ct_F77])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([ac_ct_F77])
m4trace:configure.in:13: -1- m4_pattern_allow([^ac_ct_F77$])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_LANG_SAVE' is obsolete.
You should run autoupdate.], [../../lib/autoconf/lang.m4:167: AC_LANG_SAVE is expanded from...
aclocal.m4:3992: _LT_AC_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:3991: AC_LIBTOOL_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:1786: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_LANG_RESTORE' is obsolete.
You should run autoupdate.], [../../lib/autoconf/lang.m4:176: AC_LANG_RESTORE is expanded from...
aclocal.m4:3992: _LT_AC_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:3991: AC_LIBTOOL_LANG_GCJ_CONFIG is expanded from...
aclocal.m4:1786: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_LANG_SAVE' is obsolete.
You should run autoupdate.], [../../lib/autoconf/lang.m4:167: AC_LANG_SAVE is expanded from...
aclocal.m4:4048: _LT_AC_LANG_RC_CONFIG is expanded from...
aclocal.m4:4047: AC_LIBTOOL_LANG_RC_CONFIG is expanded from...
aclocal.m4:1786: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- _m4_warn([obsolete], [The macro `AC_LANG_RESTORE' is obsolete.
You should run autoupdate.], [../../lib/autoconf/lang.m4:176: AC_LANG_RESTORE is expanded from...
aclocal.m4:4048: _LT_AC_LANG_RC_CONFIG is expanded from...
aclocal.m4:4047: AC_LIBTOOL_LANG_RC_CONFIG is expanded from...
aclocal.m4:1786: _LT_AC_TAGCONFIG is expanded from...
aclocal.m4:91: AC_LIBTOOL_SETUP is expanded from...
aclocal.m4:71: _AC_PROG_LIBTOOL is expanded from...
aclocal.m4:36: AC_PROG_LIBTOOL is expanded from...
configure.in:13: the top level])
m4trace:configure.in:13: -1- AC_SUBST([LIBTOOL])
m4trace:configure.in:13: -1- AC_SUBST_TRACE([LIBTOOL])
m4trace:configure.in:13: -1- m4_pattern_allow([^LIBTOOL$])
m4trace:configure.in:16: -1- AH_OUTPUT([HAVE_LIBDL], [/* Define to 1 if you have the `dl\' library (-ldl). */
#undef HAVE_LIBDL])
m4trace:configure.in:16: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBDL])
m4trace:configure.in:16: -1- m4_pattern_allow([^HAVE_LIBDL$])
m4trace:configure.in:17: -1- AH_OUTPUT([HAVE_LIBPTHREAD], [/* Define to 1 if you have the `pthread\' library (-lpthread). */
#undef HAVE_LIBPTHREAD])
m4trace:configure.in:17: -1- AC_DEFINE_TRACE_LITERAL([HAVE_LIBPTHREAD])
m4trace:configure.in:17: -1- m4_pattern_allow([^HAVE_LIBPTHREAD$])
m4trace:configure.in:20: -1- AC_DEFINE_TRACE_LITERAL([STDC_HEADERS])
m4trace:configure.in:20: -1- m4_pattern_allow([^STDC_HEADERS$])
m4trace:configure.in:20: -1- AH_OUTPUT([STDC_HEADERS], [/* Define to 1 if you have the ANSI C header files. */
#undef STDC_HEADERS])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_FLOAT_H], [/* Define to 1 if you have the <float.h> header file. */
#undef HAVE_FLOAT_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_INTTYPES_H], [/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_LIMITS_H], [/* Define to 1 if you have the <limits.h> header file. */
#undef HAVE_LIMITS_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_LOCALE_H], [/* Define to 1 if you have the <locale.h> header file. */
#undef HAVE_LOCALE_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_STDDEF_H], [/* Define to 1 if you have the <stddef.h> header file. */
#undef HAVE_STDDEF_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_STDINT_H], [/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_STDLIB_H], [/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_STRING_H], [/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_UNISTD_H], [/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_WCHAR_H], [/* Define to 1 if you have the <wchar.h> header file. */
#undef HAVE_WCHAR_H])
m4trace:configure.in:21: -1- AH_OUTPUT([HAVE_WCTYPE_H], [/* Define to 1 if you have the <wctype.h> header file. */
#undef HAVE_WCTYPE_H])
m4trace:configure.in:24: -1- AC_DEFINE_TRACE_LITERAL([const])
m4trace:configure.in:24: -1- m4_pattern_allow([^const$])
m4trace:configure.in:24: -1- AH_OUTPUT([const], [/* Define to empty if `const\' does not conform to ANSI C. */
#undef const])
m4trace:configure.in:25: -1- AC_DEFINE_TRACE_LITERAL([size_t])
m4trace:configure.in:25: -1- m4_pattern_allow([^size_t$])
m4trace:configure.in:25: -1- AH_OUTPUT([size_t], [/* Define to `unsigned int\' if <sys/types.h> does not define. */
#undef size_t])
m4trace:configure.in:26: -1- AC_DEFINE_TRACE_LITERAL([TM_IN_SYS_TIME])
m4trace:configure.in:26: -1- m4_pattern_allow([^TM_IN_SYS_TIME$])
m4trace:configure.in:26: -1- AH_OUTPUT([TM_IN_SYS_TIME], [/* Define to 1 if your <sys/time.h> declares `struct tm\'. */
#undef TM_IN_SYS_TIME])
m4trace:configure.in:27: -1- AC_DEFINE_TRACE_LITERAL([volatile])
m4trace:configure.in:27: -1- m4_pattern_allow([^volatile$])
m4trace:configure.in:27: -1- AH_OUTPUT([volatile], [/* Define to empty if the keyword `volatile\' does not work. Warning: valid
   code using `volatile\' can become incorrect without. Disable with care. */
#undef volatile])
m4trace:configure.in:28: -1- AC_DEFINE_TRACE_LITERAL([HAVE_PTRDIFF_T])
m4trace:configure.in:28: -1- m4_pattern_allow([^HAVE_PTRDIFF_T$])
m4trace:configure.in:28: -1- AH_OUTPUT([HAVE_PTRDIFF_T], [/* Define to 1 if the system has the type `ptrdiff_t\'. */
#undef HAVE_PTRDIFF_T])
m4trace:configure.in:31: -1- AC_DEFINE_TRACE_LITERAL([RETSIGTYPE])
m4trace:configure.in:31: -1- m4_pattern_allow([^RETSIGTYPE$])
m4trace:configure.in:31: -1- AH_OUTPUT([RETSIGTYPE], [/* Define as the return type of signal handlers (`int\' or `void\'). */
#undef RETSIGTYPE])
m4trace:configure.in:32: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRCOLL])
m4trace:configure.in:32: -1- m4_pattern_allow([^HAVE_STRCOLL$])
m4trace:configure.in:32: -1- AH_OUTPUT([HAVE_STRCOLL], [/* Define to 1 if you have the `strcoll\' function and it is properly defined.
   */
#undef HAVE_STRCOLL])
m4trace:configure.in:33: -1- AH_OUTPUT([HAVE_STRFTIME], [/* Define to 1 if you have the `strftime\' function. */
#undef HAVE_STRFTIME])
m4trace:configure.in:33: -1- AC_DEFINE_TRACE_LITERAL([HAVE_STRFTIME])
m4trace:configure.in:33: -1- m4_pattern_allow([^HAVE_STRFTIME$])
m4trace:configure.in:34: -1- AC_SUBST([POW_LIB])
m4trace:configure.in:34: -1- AC_SUBST_TRACE([POW_LIB])
m4trace:configure.in:34: -1- m4_pattern_allow([^POW_LIB$])
m4trace:configure.in:34: -1- AC_LIBSOURCE([strtod.c])
m4trace:configure.in:34: -1- AC_SUBST([LIB@&t@OBJS], ["$LIB@&t@OBJS strtod.$ac_objext"])
m4trace:configure.in:34: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.in:34: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.in:35: -1- AH_OUTPUT([HAVE_VPRINTF], [/* Define to 1 if you have the `vprintf\' function. */
#undef HAVE_VPRINTF])
m4trace:configure.in:35: -1- AC_DEFINE_TRACE_LITERAL([HAVE_DOPRNT])
m4trace:configure.in:35: -1- m4_pattern_allow([^HAVE_DOPRNT$])
m4trace:configure.in:35: -1- AH_OUTPUT([HAVE_DOPRNT], [/* Define to 1 if you don\'t have `vprintf\' but do have `_doprnt.\' */
#undef HAVE_DOPRNT])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_FLOOR], [/* Define to 1 if you have the `floor\' function. */
#undef HAVE_FLOOR])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_ISASCII], [/* Define to 1 if you have the `isascii\' function. */
#undef HAVE_ISASCII])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_LOCALECONV], [/* Define to 1 if you have the `localeconv\' function. */
#undef HAVE_LOCALECONV])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_MBLEN], [/* Define to 1 if you have the `mblen\' function. */
#undef HAVE_MBLEN])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_MEMMOVE], [/* Define to 1 if you have the `memmove\' function. */
#undef HAVE_MEMMOVE])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_MEMSET], [/* Define to 1 if you have the `memset\' function. */
#undef HAVE_MEMSET])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_MODF], [/* Define to 1 if you have the `modf\' function. */
#undef HAVE_MODF])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_POW], [/* Define to 1 if you have the `pow\' function. */
#undef HAVE_POW])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_SQRT], [/* Define to 1 if you have the `sqrt\' function. */
#undef HAVE_SQRT])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_STRCASECMP], [/* Define to 1 if you have the `strcasecmp\' function. */
#undef HAVE_STRCASECMP])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_STRCHR], [/* Define to 1 if you have the `strchr\' function. */
#undef HAVE_STRCHR])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_STRERROR], [/* Define to 1 if you have the `strerror\' function. */
#undef HAVE_STRERROR])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_STRNCASECMP], [/* Define to 1 if you have the `strncasecmp\' function. */
#undef HAVE_STRNCASECMP])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_STRRCHR], [/* Define to 1 if you have the `strrchr\' function. */
#undef HAVE_STRRCHR])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_STRSTR], [/* Define to 1 if you have the `strstr\' function. */
#undef HAVE_STRSTR])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_STRTOL], [/* Define to 1 if you have the `strtol\' function. */
#undef HAVE_STRTOL])
m4trace:configure.in:36: -1- AH_OUTPUT([HAVE_STRTOUL], [/* Define to 1 if you have the `strtoul\' function. */
#undef HAVE_STRTOUL])
m4trace:configure.in:44: -1- AC_SUBST([TALIB_LIBRARY_VERSION])
m4trace:configure.in:44: -1- AC_SUBST_TRACE([TALIB_LIBRARY_VERSION])
m4trace:configure.in:44: -1- m4_pattern_allow([^TALIB_LIBRARY_VERSION$])
m4trace:configure.in:46: -1- AC_CONFIG_FILES([Makefile src/Makefile src/ta_abstract/Makefile src/ta_common/Makefile src/ta_func/Makefile src/tools/Makefile src/tools/gen_code/Makefile src/tools/ta_regtest/Makefile ta-lib-config ta-lib.spec ta-lib.dpkg])
m4trace:configure.in:47: -1- AC_SUBST([LIB@&t@OBJS], [$ac_libobjs])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([LIB@&t@OBJS])
m4trace:configure.in:47: -1- m4_pattern_allow([^LIB@&t@OBJS$])
m4trace:configure.in:47: -1- AC_SUBST([LTLIBOBJS], [$ac_ltlibobjs])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([LTLIBOBJS])
m4trace:configure.in:47: -1- m4_pattern_allow([^LTLIBOBJS$])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([top_builddir])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([srcdir])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([abs_srcdir])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([top_srcdir])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([abs_top_srcdir])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([builddir])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([abs_builddir])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([abs_top_builddir])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([INSTALL])
m4trace:configure.in:47: -1- AC_SUBST_TRACE([MKDIR_P])
