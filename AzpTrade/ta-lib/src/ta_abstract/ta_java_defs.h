/* TA-LIB Copyright (c) 1999-2007, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/* DO NOT MODIFY this file.
 * This file is automatically generated by gen_code.
 *
 * If you are not re-generating the java code (which is most
 * likely the case), just ignore this file.
 */
 
#define TA_ACOS_Lookback acosLookback
#define TA_ACOS acos
#define TA_AD_Lookback adLookback
#define TA_AD ad
#define TA_ADD_Lookback addLookback
#define TA_ADD add
#define TA_ADOSC_Lookback adOscLookback
#define TA_ADOSC adOsc
#define TA_ADX_Lookback adxLookback
#define TA_ADX adx
#define TA_ADXR_Lookback adxrLookback
#define TA_ADXR adxr
#define TA_APO_Lookback apoLookback
#define TA_APO apo
#define TA_AROON_Lookback aroonLookback
#define TA_AROON aroon
#define TA_AROONOSC_Lookback aroonOscLookback
#define TA_AROONOSC aroonOsc
#define TA_ASIN_Lookback asinLookback
#define TA_ASIN asin
#define TA_ATAN_Lookback atanLookback
#define TA_ATAN atan
#define TA_ATR_Lookback atrLookback
#define TA_ATR atr
#define TA_AVGPRICE_Lookback avgPriceLookback
#define TA_AVGPRICE avgPrice
#define TA_BBANDS_Lookback bbandsLookback
#define TA_BBANDS bbands
#define TA_BETA_Lookback betaLookback
#define TA_BETA beta
#define TA_BOP_Lookback bopLookback
#define TA_BOP bop
#define TA_CCI_Lookback cciLookback
#define TA_CCI cci
#define TA_CDL2CROWS_Lookback cdl2CrowsLookback
#define TA_CDL2CROWS cdl2Crows
#define TA_CDL3BLACKCROWS_Lookback cdl3BlackCrowsLookback
#define TA_CDL3BLACKCROWS cdl3BlackCrows
#define TA_CDL3INSIDE_Lookback cdl3InsideLookback
#define TA_CDL3INSIDE cdl3Inside
#define TA_CDL3LINESTRIKE_Lookback cdl3LineStrikeLookback
#define TA_CDL3LINESTRIKE cdl3LineStrike
#define TA_CDL3OUTSIDE_Lookback cdl3OutsideLookback
#define TA_CDL3OUTSIDE cdl3Outside
#define TA_CDL3STARSINSOUTH_Lookback cdl3StarsInSouthLookback
#define TA_CDL3STARSINSOUTH cdl3StarsInSouth
#define TA_CDL3WHITESOLDIERS_Lookback cdl3WhiteSoldiersLookback
#define TA_CDL3WHITESOLDIERS cdl3WhiteSoldiers
#define TA_CDLABANDONEDBABY_Lookback cdlAbandonedBabyLookback
#define TA_CDLABANDONEDBABY cdlAbandonedBaby
#define TA_CDLADVANCEBLOCK_Lookback cdlAdvanceBlockLookback
#define TA_CDLADVANCEBLOCK cdlAdvanceBlock
#define TA_CDLBELTHOLD_Lookback cdlBeltHoldLookback
#define TA_CDLBELTHOLD cdlBeltHold
#define TA_CDLBREAKAWAY_Lookback cdlBreakawayLookback
#define TA_CDLBREAKAWAY cdlBreakaway
#define TA_CDLCLOSINGMARUBOZU_Lookback cdlClosingMarubozuLookback
#define TA_CDLCLOSINGMARUBOZU cdlClosingMarubozu
#define TA_CDLCONCEALBABYSWALL_Lookback cdlConcealBabysWallLookback
#define TA_CDLCONCEALBABYSWALL cdlConcealBabysWall
#define TA_CDLCOUNTERATTACK_Lookback cdlCounterAttackLookback
#define TA_CDLCOUNTERATTACK cdlCounterAttack
#define TA_CDLDARKCLOUDCOVER_Lookback cdlDarkCloudCoverLookback
#define TA_CDLDARKCLOUDCOVER cdlDarkCloudCover
#define TA_CDLDOJI_Lookback cdlDojiLookback
#define TA_CDLDOJI cdlDoji
#define TA_CDLDOJISTAR_Lookback cdlDojiStarLookback
#define TA_CDLDOJISTAR cdlDojiStar
#define TA_CDLDRAGONFLYDOJI_Lookback cdlDragonflyDojiLookback
#define TA_CDLDRAGONFLYDOJI cdlDragonflyDoji
#define TA_CDLENGULFING_Lookback cdlEngulfingLookback
#define TA_CDLENGULFING cdlEngulfing
#define TA_CDLEVENINGDOJISTAR_Lookback cdlEveningDojiStarLookback
#define TA_CDLEVENINGDOJISTAR cdlEveningDojiStar
#define TA_CDLEVENINGSTAR_Lookback cdlEveningStarLookback
#define TA_CDLEVENINGSTAR cdlEveningStar
#define TA_CDLGAPSIDESIDEWHITE_Lookback cdlGapSideSideWhiteLookback
#define TA_CDLGAPSIDESIDEWHITE cdlGapSideSideWhite
#define TA_CDLGRAVESTONEDOJI_Lookback cdlGravestoneDojiLookback
#define TA_CDLGRAVESTONEDOJI cdlGravestoneDoji
#define TA_CDLHAMMER_Lookback cdlHammerLookback
#define TA_CDLHAMMER cdlHammer
#define TA_CDLHANGINGMAN_Lookback cdlHangingManLookback
#define TA_CDLHANGINGMAN cdlHangingMan
#define TA_CDLHARAMI_Lookback cdlHaramiLookback
#define TA_CDLHARAMI cdlHarami
#define TA_CDLHARAMICROSS_Lookback cdlHaramiCrossLookback
#define TA_CDLHARAMICROSS cdlHaramiCross
#define TA_CDLHIGHWAVE_Lookback cdlHignWaveLookback
#define TA_CDLHIGHWAVE cdlHignWave
#define TA_CDLHIKKAKE_Lookback cdlHikkakeLookback
#define TA_CDLHIKKAKE cdlHikkake
#define TA_CDLHIKKAKEMOD_Lookback cdlHikkakeModLookback
#define TA_CDLHIKKAKEMOD cdlHikkakeMod
#define TA_CDLHOMINGPIGEON_Lookback cdlHomingPigeonLookback
#define TA_CDLHOMINGPIGEON cdlHomingPigeon
#define TA_CDLIDENTICAL3CROWS_Lookback cdlIdentical3CrowsLookback
#define TA_CDLIDENTICAL3CROWS cdlIdentical3Crows
#define TA_CDLINNECK_Lookback cdlInNeckLookback
#define TA_CDLINNECK cdlInNeck
#define TA_CDLINVERTEDHAMMER_Lookback cdlInvertedHammerLookback
#define TA_CDLINVERTEDHAMMER cdlInvertedHammer
#define TA_CDLKICKING_Lookback cdlKickingLookback
#define TA_CDLKICKING cdlKicking
#define TA_CDLKICKINGBYLENGTH_Lookback cdlKickingByLengthLookback
#define TA_CDLKICKINGBYLENGTH cdlKickingByLength
#define TA_CDLLADDERBOTTOM_Lookback cdlLadderBottomLookback
#define TA_CDLLADDERBOTTOM cdlLadderBottom
#define TA_CDLLONGLEGGEDDOJI_Lookback cdlLongLeggedDojiLookback
#define TA_CDLLONGLEGGEDDOJI cdlLongLeggedDoji
#define TA_CDLLONGLINE_Lookback cdlLongLineLookback
#define TA_CDLLONGLINE cdlLongLine
#define TA_CDLMARUBOZU_Lookback cdlMarubozuLookback
#define TA_CDLMARUBOZU cdlMarubozu
#define TA_CDLMATCHINGLOW_Lookback cdlMatchingLowLookback
#define TA_CDLMATCHINGLOW cdlMatchingLow
#define TA_CDLMATHOLD_Lookback cdlMatHoldLookback
#define TA_CDLMATHOLD cdlMatHold
#define TA_CDLMORNINGDOJISTAR_Lookback cdlMorningDojiStarLookback
#define TA_CDLMORNINGDOJISTAR cdlMorningDojiStar
#define TA_CDLMORNINGSTAR_Lookback cdlMorningStarLookback
#define TA_CDLMORNINGSTAR cdlMorningStar
#define TA_CDLONNECK_Lookback cdlOnNeckLookback
#define TA_CDLONNECK cdlOnNeck
#define TA_CDLPIERCING_Lookback cdlPiercingLookback
#define TA_CDLPIERCING cdlPiercing
#define TA_CDLRICKSHAWMAN_Lookback cdlRickshawManLookback
#define TA_CDLRICKSHAWMAN cdlRickshawMan
#define TA_CDLRISEFALL3METHODS_Lookback cdlRiseFall3MethodsLookback
#define TA_CDLRISEFALL3METHODS cdlRiseFall3Methods
#define TA_CDLSEPARATINGLINES_Lookback cdlSeperatingLinesLookback
#define TA_CDLSEPARATINGLINES cdlSeperatingLines
#define TA_CDLSHOOTINGSTAR_Lookback cdlShootingStarLookback
#define TA_CDLSHOOTINGSTAR cdlShootingStar
#define TA_CDLSHORTLINE_Lookback cdlShortLineLookback
#define TA_CDLSHORTLINE cdlShortLine
#define TA_CDLSPINNINGTOP_Lookback cdlSpinningTopLookback
#define TA_CDLSPINNINGTOP cdlSpinningTop
#define TA_CDLSTALLEDPATTERN_Lookback cdlStalledPatternLookback
#define TA_CDLSTALLEDPATTERN cdlStalledPattern
#define TA_CDLSTICKSANDWICH_Lookback cdlStickSandwhichLookback
#define TA_CDLSTICKSANDWICH cdlStickSandwhich
#define TA_CDLTAKURI_Lookback cdlTakuriLookback
#define TA_CDLTAKURI cdlTakuri
#define TA_CDLTASUKIGAP_Lookback cdlTasukiGapLookback
#define TA_CDLTASUKIGAP cdlTasukiGap
#define TA_CDLTHRUSTING_Lookback cdlThrustingLookback
#define TA_CDLTHRUSTING cdlThrusting
#define TA_CDLTRISTAR_Lookback cdlTristarLookback
#define TA_CDLTRISTAR cdlTristar
#define TA_CDLUNIQUE3RIVER_Lookback cdlUnique3RiverLookback
#define TA_CDLUNIQUE3RIVER cdlUnique3River
#define TA_CDLUPSIDEGAP2CROWS_Lookback cdlUpsideGap2CrowsLookback
#define TA_CDLUPSIDEGAP2CROWS cdlUpsideGap2Crows
#define TA_CDLXSIDEGAP3METHODS_Lookback cdlXSideGap3MethodsLookback
#define TA_CDLXSIDEGAP3METHODS cdlXSideGap3Methods
#define TA_CEIL_Lookback ceilLookback
#define TA_CEIL ceil
#define TA_CMO_Lookback cmoLookback
#define TA_CMO cmo
#define TA_CORREL_Lookback correlLookback
#define TA_CORREL correl
#define TA_COS_Lookback cosLookback
#define TA_COS cos
#define TA_COSH_Lookback coshLookback
#define TA_COSH cosh
#define TA_DEMA_Lookback demaLookback
#define TA_DEMA dema
#define TA_DIV_Lookback divLookback
#define TA_DIV div
#define TA_DX_Lookback dxLookback
#define TA_DX dx
#define TA_EMA_Lookback emaLookback
#define TA_EMA ema
#define TA_EXP_Lookback expLookback
#define TA_EXP exp
#define TA_FLOOR_Lookback floorLookback
#define TA_FLOOR floor
#define TA_HT_DCPERIOD_Lookback htDcPeriodLookback
#define TA_HT_DCPERIOD htDcPeriod
#define TA_HT_DCPHASE_Lookback htDcPhaseLookback
#define TA_HT_DCPHASE htDcPhase
#define TA_HT_PHASOR_Lookback htPhasorLookback
#define TA_HT_PHASOR htPhasor
#define TA_HT_SINE_Lookback htSineLookback
#define TA_HT_SINE htSine
#define TA_HT_TRENDLINE_Lookback htTrendlineLookback
#define TA_HT_TRENDLINE htTrendline
#define TA_HT_TRENDMODE_Lookback htTrendModeLookback
#define TA_HT_TRENDMODE htTrendMode
#define TA_KAMA_Lookback kamaLookback
#define TA_KAMA kama
#define TA_LINEARREG_Lookback linearRegLookback
#define TA_LINEARREG linearReg
#define TA_LINEARREG_ANGLE_Lookback linearRegAngleLookback
#define TA_LINEARREG_ANGLE linearRegAngle
#define TA_LINEARREG_INTERCEPT_Lookback linearRegInterceptLookback
#define TA_LINEARREG_INTERCEPT linearRegIntercept
#define TA_LINEARREG_SLOPE_Lookback linearRegSlopeLookback
#define TA_LINEARREG_SLOPE linearRegSlope
#define TA_LN_Lookback lnLookback
#define TA_LN ln
#define TA_LOG10_Lookback log10Lookback
#define TA_LOG10 log10
#define TA_MA_Lookback movingAverageLookback
#define TA_MA movingAverage
#define TA_MACD_Lookback macdLookback
#define TA_MACD macd
#define TA_MACDEXT_Lookback macdExtLookback
#define TA_MACDEXT macdExt
#define TA_MACDFIX_Lookback macdFixLookback
#define TA_MACDFIX macdFix
#define TA_MAMA_Lookback mamaLookback
#define TA_MAMA mama
#define TA_MAVP_Lookback movingAverageVariablePeriodLookback
#define TA_MAVP movingAverageVariablePeriod
#define TA_MAX_Lookback maxLookback
#define TA_MAX max
#define TA_MAXINDEX_Lookback maxIndexLookback
#define TA_MAXINDEX maxIndex
#define TA_MEDPRICE_Lookback medPriceLookback
#define TA_MEDPRICE medPrice
#define TA_MFI_Lookback mfiLookback
#define TA_MFI mfi
#define TA_MIDPOINT_Lookback midPointLookback
#define TA_MIDPOINT midPoint
#define TA_MIDPRICE_Lookback midPriceLookback
#define TA_MIDPRICE midPrice
#define TA_MIN_Lookback minLookback
#define TA_MIN min
#define TA_MININDEX_Lookback minIndexLookback
#define TA_MININDEX minIndex
#define TA_MINMAX_Lookback minMaxLookback
#define TA_MINMAX minMax
#define TA_MINMAXINDEX_Lookback minMaxIndexLookback
#define TA_MINMAXINDEX minMaxIndex
#define TA_MINUS_DI_Lookback minusDILookback
#define TA_MINUS_DI minusDI
#define TA_MINUS_DM_Lookback minusDMLookback
#define TA_MINUS_DM minusDM
#define TA_MOM_Lookback momLookback
#define TA_MOM mom
#define TA_MULT_Lookback multLookback
#define TA_MULT mult
#define TA_NATR_Lookback natrLookback
#define TA_NATR natr
#define TA_OBV_Lookback obvLookback
#define TA_OBV obv
#define TA_PLUS_DI_Lookback plusDILookback
#define TA_PLUS_DI plusDI
#define TA_PLUS_DM_Lookback plusDMLookback
#define TA_PLUS_DM plusDM
#define TA_PPO_Lookback ppoLookback
#define TA_PPO ppo
#define TA_ROC_Lookback rocLookback
#define TA_ROC roc
#define TA_ROCP_Lookback rocPLookback
#define TA_ROCP rocP
#define TA_ROCR_Lookback rocRLookback
#define TA_ROCR rocR
#define TA_ROCR100_Lookback rocR100Lookback
#define TA_ROCR100 rocR100
#define TA_RSI_Lookback rsiLookback
#define TA_RSI rsi
#define TA_SAR_Lookback sarLookback
#define TA_SAR sar
#define TA_SAREXT_Lookback sarExtLookback
#define TA_SAREXT sarExt
#define TA_SIN_Lookback sinLookback
#define TA_SIN sin
#define TA_SINH_Lookback sinhLookback
#define TA_SINH sinh
#define TA_SMA_Lookback smaLookback
#define TA_SMA sma
#define TA_SQRT_Lookback sqrtLookback
#define TA_SQRT sqrt
#define TA_STDDEV_Lookback stdDevLookback
#define TA_STDDEV stdDev
#define TA_STOCH_Lookback stochLookback
#define TA_STOCH stoch
#define TA_STOCHF_Lookback stochFLookback
#define TA_STOCHF stochF
#define TA_STOCHRSI_Lookback stochRsiLookback
#define TA_STOCHRSI stochRsi
#define TA_SUB_Lookback subLookback
#define TA_SUB sub
#define TA_SUM_Lookback sumLookback
#define TA_SUM sum
#define TA_T3_Lookback t3Lookback
#define TA_T3 t3
#define TA_TAN_Lookback tanLookback
#define TA_TAN tan
#define TA_TANH_Lookback tanhLookback
#define TA_TANH tanh
#define TA_TEMA_Lookback temaLookback
#define TA_TEMA tema
#define TA_TRANGE_Lookback trueRangeLookback
#define TA_TRANGE trueRange
#define TA_TRIMA_Lookback trimaLookback
#define TA_TRIMA trima
#define TA_TRIX_Lookback trixLookback
#define TA_TRIX trix
#define TA_TSF_Lookback tsfLookback
#define TA_TSF tsf
#define TA_TYPPRICE_Lookback typPriceLookback
#define TA_TYPPRICE typPrice
#define TA_ULTOSC_Lookback ultOscLookback
#define TA_ULTOSC ultOsc
#define TA_VAR_Lookback varianceLookback
#define TA_VAR variance
#define TA_WCLPRICE_Lookback wclPriceLookback
#define TA_WCLPRICE wclPrice
#define TA_WILLR_Lookback willRLookback
#define TA_WILLR willR
#define TA_WMA_Lookback wmaLookback
#define TA_WMA wma

/***************
 * End of File *
 ***************/
