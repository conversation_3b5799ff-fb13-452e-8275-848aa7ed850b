# -*- coding: utf-8 -*-
import os
import sys
import pandas as pd
import psycopg2
import datetime
import traceback

# 获取项目根目录并添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))  # AzpTrade目录
if project_root not in sys.path:
    sys.path.insert(0, project_root)



def query_latest_data(limit=20, output_file=None):
    """
    查询最近的股票数据并导出为CSV
    
    Args:
        limit: 查询记录数量限制
        output_file: 输出文件路径，如果为None则使用时间戳生成文件名
    
    Returns:
        输出文件路径
    """
    try:
        # 数据库连接参数
        conn_params = {
            "dbname": "stock_data",
            "user": "postgres",
            "password": "MQH1ylhedpia7oM5",  # 数据库密码
            "host": "localhost",
            "port": "5432"
        }
        
        # 连接数据库
        conn = psycopg2.connect(**conn_params)
        cur = conn.cursor()
        
        # 查询最近数据的时间点
        cur.execute("SELECT MAX(time) FROM stock_prices;")
        latest_time = cur.fetchone()[0]
        
        if not latest_time:
            print("数据库中没有数据")
            return None
        
        print(f"最新数据时间点: {latest_time}")
        
        # 查询SQL - 获取所有字段
        # 注意：我们需要使用双引号来保留列名的大小写
        query = """
        SELECT time, symbol, name, open, close, high, low, 
               "turnOverRate", "openChangePercent", "changeRate", "changePercent",
               volume, "volumeAmount", "isLimitUp", "isLimitDown", "lianbanCount",
               "conceptName", "boardChangePercent"
        FROM stock_prices 
        WHERE time = %s
        LIMIT %s;
        """
        
        # 执行查询
        cur.execute(query, (latest_time, limit))
        rows = cur.fetchall()
        
        # 获取列名
        column_names = [desc[0] for desc in cur.description]
        
        # 关闭数据库连接
        cur.close()
        conn.close()
        
        # 转换为DataFrame
        df = pd.DataFrame(rows, columns=column_names)
        
        # 设置输出文件名
        if not output_file:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            # 保存到与脚本相同的目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            output_file = os.path.join(script_dir, f"latest_stock_data_{timestamp}.csv")
        
        # 导出为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')  # 使用utf-8-sig以支持Excel直接打开中文
        
        print(f"成功查询到 {len(rows)} 条记录")
        print(f"数据已导出到: {output_file}")
        
        return output_file
    
    except Exception as e:
        print(f"查询数据失败: {e}")
        traceback.print_exc()
        return None


def query_stock_by_symbol(symbol, limit=20, output_file=None):
    """
    查询特定股票的最近数据并导出为CSV
    
    Args:
        symbol: 股票代码
        limit: 查询记录数量限制
        output_file: 输出文件路径，如果为None则使用时间戳和股票代码生成文件名
    
    Returns:
        输出文件路径
    """
    try:
        # 数据库连接参数
        conn_params = {
            "dbname": "stock_data",
            "user": "postgres",
            "password": "MQH1ylhedpia7oM5",  # 数据库密码
            "host": "localhost",
            "port": "5432"
        }
        
        # 连接数据库
        conn = psycopg2.connect(**conn_params)
        cur = conn.cursor()
        
        # 查询SQL - 获取指定股票的历史数据
        query = """
        SELECT time, symbol, name, open, close, high, low, 
               "turnOverRate", "openChangePercent", "changeRate", "changePercent",
               volume, "volumeAmount", "isLimitUp", "isLimitDown", "lianbanCount",
               "conceptName", "boardChangePercent"
        FROM stock_prices 
        WHERE symbol = %s
        ORDER BY time DESC
        LIMIT %s;
        """
        
        # 执行查询
        cur.execute(query, (symbol, limit))
        rows = cur.fetchall()
        
        # 获取列名
        column_names = [desc[0] for desc in cur.description]
        
        # 关闭数据库连接
        cur.close()
        conn.close()
        
        # 转换为DataFrame
        df = pd.DataFrame(rows, columns=column_names)
        
        # 设置输出文件名
        if not output_file:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            # 保存到与脚本相同的目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            output_file = os.path.join(script_dir, f"{symbol}_stock_data_{timestamp}.csv")
        
        # 导出为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')  # 使用utf-8-sig以支持Excel直接打开中文
        
        print(f"成功查询到股票 {symbol} 的 {len(rows)} 条记录")
        print(f"数据已导出到: {output_file}")
        
        return output_file
    
    except Exception as e:
        print(f"查询数据失败: {e}")
        traceback.print_exc()
        return None


def query_concept_stocks(concept_name, limit=20, output_file=None):
    """
    查询特定概念的股票数据并导出为CSV
    
    Args:
        concept_name: 概念名称(支持模糊匹配)
        limit: 查询记录数量限制
        output_file: 输出文件路径，如果为None则使用时间戳和概念名生成文件名
    
    Returns:
        输出文件路径
    """
    try:
        # 数据库连接参数
        conn_params = {
            "dbname": "stock_data",
            "user": "postgres",
            "password": "MQH1ylhedpia7oM5",  # 数据库密码
            "host": "localhost",
            "port": "5432"
        }
        
        # 连接数据库
        conn = psycopg2.connect(**conn_params)
        cur = conn.cursor()
        
        # 获取最新时间点
        cur.execute("SELECT MAX(time) FROM stock_prices;")
        latest_time = cur.fetchone()[0]
        
        if not latest_time:
            print("数据库中没有数据")
            return None
            
        # 查询SQL - 获取特定概念的股票
        query = """
        SELECT time, symbol, name, open, close, high, low, 
               "turnOverRate", "changePercent", "isLimitUp", "lianbanCount",
               "conceptName", "conceptChangePercent", "boardChangePercent"
        FROM stock_prices 
        WHERE time = %s AND "conceptName" LIKE %s
        ORDER BY "changePercent" DESC
        LIMIT %s;
        """
        
        # 执行查询
        pattern = f'%{concept_name}%'  # 模糊匹配
        cur.execute(query, (latest_time, pattern, limit))
        rows = cur.fetchall()
        
        # 获取列名
        column_names = [desc[0] for desc in cur.description]
        
        # 关闭数据库连接
        cur.close()
        conn.close()
        
        # 转换为DataFrame
        df = pd.DataFrame(rows, columns=column_names)
        
        # 设置输出文件名
        if not output_file:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            concept_filename = concept_name.replace(' ', '_').replace('/', '_')
            # 保存到与脚本相同的目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            output_file = os.path.join(script_dir, f"{concept_filename}_stocks_{timestamp}.csv")
        
        # 导出为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')  # 使用utf-8-sig以支持Excel直接打开中文
        
        print(f"成功查询到概念 '{concept_name}' 的 {len(rows)} 条记录")
        print(f"数据已导出到: {output_file}")
        
        return output_file
    
    except Exception as e:
        print(f"查询数据失败: {e}")
        traceback.print_exc()
        return None


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="查询股票数据并导出为CSV")
    parser.add_argument("--type", choices=["latest", "stock", "concept"], default="latest", help="查询类型: latest=最新数据, stock=特定股票, concept=特定概念")
    parser.add_argument("--value", help="查询值: 股票代码或概念名称")
    parser.add_argument("--limit", type=int, default=20, help="查询记录数量限制")
    parser.add_argument("--output", help="输出文件路径")
    
    args = parser.parse_args()
    
    if args.type == "latest":
        query_latest_data(limit=args.limit, output_file=args.output)
    elif args.type == "stock":
        if not args.value:
            print("使用--stock类型时必须提供--value参数指定股票代码")
        else:
            query_stock_by_symbol(args.value, limit=args.limit, output_file=args.output)
    elif args.type == "concept":
        if not args.value:
            print("使用--concept类型时必须提供--value参数指定概念名称")
        else:
            query_concept_stocks(args.value, limit=args.limit, output_file=args.output)
