def get_stock_main_inflow(symbol, date, measure_time=False):
    """
    获取指定股票在特定日期的主净流入数据，并过滤掉休市时间（11:30-13:00）
    
    参数:
        symbol: 股票代码，例如'000001'
        date: 日期，格式为'YYYY-MM-DD'字符串或datetime.date对象
        measure_time: 是否测量耗时，默认为False
    
    返回:
        DataFrame对象，包含时间和主净流入数据，已去除休市时间段
    """
    import time
    
    if measure_time:
        start_time = time.time()
    
    try:
        # 连接数据库
        conn_params = {
            'dbname': 'stock_data',
            'user': 'postgres',
            'password': 'MQH1ylhedpia7oM5',
            'host': 'localhost',
            'port': '5432'
        }
        
        # 构建SQL查询
        sql = """
        SELECT time, "mainInFlowAmount"
        FROM stock_prices
        WHERE symbol = %s
          AND time::date = %s
        ORDER BY time
        """
        
        # 执行查询
        import psycopg2
        import pandas as pd
        import pytz
        
        conn = psycopg2.connect(**conn_params)
        df = pd.read_sql(sql, conn, params=(symbol, date))
        conn.close()
        
        # 检查查询结果
        if df.empty:
            print(f"没有找到 {symbol} 在 {date} 的主净流入数据")
            return None
        
        # 转换时区为北京时间（UTC+8）
        if not pd.api.types.is_datetime64_any_dtype(df['time']):
            df['time'] = pd.to_datetime(df['time'])
        
        # 如果时间没有时区信息，则假设为UTC时间
        if df['time'].dt.tz is None:
            df['time'] = df['time'].dt.tz_localize('UTC')
        
        # 转换为北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        df['time'] = df['time'].dt.tz_convert(beijing_tz)
        
        # 去除时区信息，保留北京时间的时间值
        df['time'] = df['time'].dt.tz_localize(None)
        
        # 过滤掉休市时间（11:30-13:00）
        df['hour'] = df['time'].dt.hour
        df['minute'] = df['time'].dt.minute
        df['time_value'] = df['hour'] * 100 + df['minute']  # 创建一个数值来比较时间，例如1130表示11:30
        
        # 保留非休市时间的数据（时间值小于1130或大于等于1300）
        df_filtered = df[(df['time_value'] < 1130) | (df['time_value'] >= 1300)]
        
        # 删除辅助列
        df_filtered = df_filtered.drop(['hour', 'minute', 'time_value'], axis=1)
        
        # 输出过滤后的数据条数
        print(f"查询到 {len(df)} 条 {symbol} 在 {date} 的主净流入数据，去除休市时间后剩余 {len(df_filtered)} 条")
        
        # 如果需要测量时间，计算并输出耗时
        if measure_time:
            end_time = time.time()
            elapsed_time = (end_time - start_time) * 1000  # 转换为毫秒
            print(f"查询 {symbol} 的主净流入数据耗时: {elapsed_time:.2f} 毫秒")
        
        return df_filtered
        
    except Exception as e:
        print(f"查询失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None
        

def plot_stock_main_inflow(symbol, date, save_dir=None):
    """
    绘制指定股票在特定日期的主净流入数据时间序列图
    
    参数:
        symbol: 股票代码，例如'000001'
        date: 日期，格式为'YYYY-MM-DD'字符串或datetime.date对象
        save_dir: 保存图片的目录，如果为None则不保存
    """
    import matplotlib.pyplot as plt
    import pandas as pd
    import matplotlib.dates as mdates
    from matplotlib.ticker import FuncFormatter
    import os
    from datetime import datetime
    from matplotlib import rcParams
    
    # 置空字体设置，使用默认字体
    plt.rc('font', family='DejaVu Sans')
    rcParams['axes.unicode_minus'] = False
    
    # 查询数据
    df = get_stock_main_inflow(symbol, date)
    if df is None or df.empty:
        print(f"没有找到 {symbol} 在 {date} 的主净流入数据，无法绘图")
        return
    
    # 数据预处理
    # 将mainInFlowAmount转换为以万元为单位
    df['mainInFlowAmount_wan'] = df['mainInFlowAmount'] / 10000
    
    # 得到上午和下午时间段的数据
    am_data = df[(df['time'].dt.hour < 12) | 
                ((df['time'].dt.hour == 11) & (df['time'].dt.minute < 30))]
    pm_data = df[(df['time'].dt.hour >= 13)]
    
    # 重新创建图形
    fig, ax1 = plt.subplots(figsize=(12, 6))
    
    # 添加零线
    ax1.axhline(y=0, color='r', linestyle='--', alpha=0.3)
    
    # 分别绘制上午和下午的数据
    if not am_data.empty:
        ax1.plot(am_data['time'], am_data['mainInFlowAmount_wan'], 'b-', linewidth=1.5)
    
    if not pm_data.empty:
        ax1.plot(pm_data['time'], pm_data['mainInFlowAmount_wan'], 'b-', linewidth=1.5)
    
    # 设置标题和标签
    ax1.set_title(f'Main Capital Flow for Stock {symbol} on {date} (10,000 CNY)')
    ax1.set_xlabel('Time')
    ax1.set_ylabel('Main Capital Flow (10,000 CNY)')
    
    # 格式化Y轴，显示为万元
    def millions(x, pos):
        return f'{x:.0f}'
    
    ax1.yaxis.set_major_formatter(FuncFormatter(millions))
    
    # 添加累计净流入曲线（第二Y轴）
    ax2 = ax1.twinx()
    
    # 对上午和下午分别绘制累计流量
    if not am_data.empty:
        am_cumulative = am_data['mainInFlowAmount'].cumsum() / 10000
        ax2.plot(am_data['time'], am_cumulative, 'g-', linewidth=1.5, alpha=0.7)
    
    if not pm_data.empty:
        # 确保下午的累计从上午的最后一个值继续
        if not am_data.empty:
            pm_start_cumulative = am_data['mainInFlowAmount'].sum() / 10000
        else:
            pm_start_cumulative = 0
            
        pm_data['adjusted_cumulative'] = pm_data['mainInFlowAmount'].cumsum() / 10000 + pm_start_cumulative
        ax2.plot(pm_data['time'], pm_data['adjusted_cumulative'], 'g-', linewidth=1.5, alpha=0.7)
    
    ax2.set_ylabel('Cumulative Flow (10,000 CNY)', color='g')
    ax2.tick_params(axis='y', labelcolor='g')
    ax2.yaxis.set_major_formatter(FuncFormatter(millions))
    
    # 设置X轴时间格式和刻度
    fig.autofmt_xdate()
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    
    # 设置合适的刻度间隔，每小时一个刻度
    hours = [9, 10, 11, 13, 14, 15]  # 交易时间的小时
    ax1.xaxis.set_major_locator(mdates.HourLocator(byhour=hours))
    
    # 添加网格
    ax1.grid(True, alpha=0.3)
    
    # 紧凑布局
    plt.tight_layout()
    
    # 如果提供了保存目录，则保存图片
    if save_dir:
        # 确保目录存在
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            print(f"创建目录: {save_dir}")
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{symbol}_{date}_mainflow_{timestamp}.png"
        filepath = os.path.join(save_dir, filename)
        
        # 保存图片
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"图片已保存到: {filepath}")
    
    # 显示图形
    plt.show()

if __name__ == "__main__":
    # 查询平安银行(000001)在2025-05-28的主净数据
    import datetime
    import pandas as pd
    import os
    
    # 创建保存图表的目录
    stats_dir = "/root/azptrade/AzpTrade/data_analysis/stats"
    img_dir = os.path.join(stats_dir, "stock_flows")
    
    # 如果目录不存在，创建它
    if not os.path.exists(img_dir):
        os.makedirs(img_dir)
        print(f"创建统计图表目录: {img_dir}")
    
    # 获取昨天的日期
    today = datetime.date.today()
    yesterday = today - datetime.timedelta(days=1)
    
    # 选择要查询的股票和日期
    symbol = '603518'  # 光迅科技
    date = '2025-05-29'
    
    # 查询数据
    df = get_stock_main_inflow(symbol, date, measure_time=True)
    
    # 打印前50条和后50条数据
    if df is not None and not df.empty:
        # 本地化时间输出格式
        pd.set_option('display.float_format', '{:.2f}'.format)
        
        # 计算万元单位的数值
        df['mainInFlowAmount_wan'] = df['mainInFlowAmount'] / 10000
        
        # 打印前50条数据
        print(f"\n====== 前50条{symbol}股票的主净流入数据(万元) ======")
        first_50 = df.head(50)[['time', 'mainInFlowAmount_wan']]
        first_50.columns = ['时间', '主净流入(万元)']
        print(first_50.to_string(index=False))
        
        # 打印后50条数据
        print(f"\n====== 后50条{symbol}股票的主净流入数据(万元) ======")
        last_50 = df.tail(50)[['time', 'mainInFlowAmount_wan']]
        last_50.columns = ['时间', '主净流入(万元)']
        print(last_50.to_string(index=False))
        
        # 计算当天累计净流入
        # 直接取最后一条数据的值，因为每个时间点的值已经是总的
        if 'mainInFlowAmount' in df.columns:
            # 取最后一个时间点的值作为当天总计
            total_inflow = df['mainInFlowAmount'].iloc[-1] / 10000
            print(f"\n当天净流入: {total_inflow:.2f}万元")
    
    # 绘制图表并保存
    print(f"\n绘制 {symbol} 在 {date} 的主净流入图表并保存...")
    plot_stock_main_inflow(symbol, date, save_dir=img_dir)
    
    # 也可以尝试查询昨天的数据并绘图
    print("\n查询昨天的数据并绘制图表...")
    plot_stock_main_inflow(symbol, yesterday, save_dir=img_dir)
    
    print("\n图表已保存到目录: " + img_dir)