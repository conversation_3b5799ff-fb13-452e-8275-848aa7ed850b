# -*- coding: utf-8 -*-
import psycopg2
import traceback
import datetime

def query_database_size():
    """
    查询数据库和表的大小
    
    Returns:
        dict: 包含数据库和表大小信息的字典
    """
    try:
        # 数据库连接参数
        conn_params = {
            "dbname": "stock_data",
            "user": "postgres",
            "password": "MQH1ylhedpia7oM5",  # 数据库密码
            "host": "localhost",
            "port": "5432"
        }
        
        # 连接数据库
        conn = psycopg2.connect(**conn_params)
        cur = conn.cursor()
        
        # 查询数据库大小
        cur.execute("SELECT pg_size_pretty(pg_database_size(current_database()));")
        db_size = cur.fetchone()[0]
        
        # 查询具体表的大小
        cur.execute("""
        SELECT 
            table_name, 
            pg_size_pretty(pg_total_relation_size(quote_ident(table_name))),
            pg_total_relation_size(quote_ident(table_name))
        FROM information_schema.tables
        WHERE table_schema = 'public'
        ORDER BY pg_total_relation_size(quote_ident(table_name)) DESC;
        """)
        tables_size = cur.fetchall()
        
        # 查询每天的数据量
        cur.execute("""
        SELECT 
            DATE(time) as date,
            COUNT(*) as record_count,
            pg_size_pretty(SUM(pg_column_size(t.*))) as estimated_size
        FROM stock_prices t
        GROUP BY DATE(time)
        ORDER BY date DESC
        LIMIT 10;
        """)
        daily_stats = cur.fetchall()
        
        # 获取每条记录的平均大小
        cur.execute("""
        SELECT 
            pg_size_pretty(AVG(pg_column_size(t.*))) as avg_row_size,
            AVG(pg_column_size(t.*)) as avg_row_bytes
        FROM stock_prices t
        LIMIT 1000;
        """)
        avg_row_size = cur.fetchone()
        
        # 查询数据库中所有表的总记录数
        cur.execute("""
        SELECT 
            relname as table_name,
            n_live_tup as row_count
        FROM pg_stat_user_tables
        ORDER BY n_live_tup DESC;
        """)
        total_records = cur.fetchall()
        
        # 关闭数据库连接
        cur.close()
        conn.close()
        
        # 打印结果
        print(f"\n数据库总大小: {db_size}")
        
        print("\n各表大小:")
        for table_name, size_pretty, size_bytes in tables_size:
            print(f"{table_name}: {size_pretty}")
        
        print(f"\n每条记录平均大小: {avg_row_size[0]}, {avg_row_size[1]} 字节")
        
        print("\n表记录数:")
        for table_name, row_count in total_records:
            print(f"{table_name}: {row_count:,} 条记录")
        
        print("\n最近10天数据统计:")
        for date, count, size in daily_stats:
            print(f"{date}: {count:,} 条记录, 大小约 {size}")
        
        # 计算存储1年数据需要的空间
        if daily_stats and len(daily_stats) > 0:
            try:
                # 取最近一天的数据量作为估算基准
                recent_day_count = daily_stats[0][1]
                avg_bytes_per_row = avg_row_size[1]
                
                # 估算一年的交易日数（约245天）
                trading_days_per_year = 245
                
                # 计算一年的总数据量
                yearly_records = recent_day_count * trading_days_per_year
                yearly_size_bytes = yearly_records * avg_bytes_per_row
                
                # 将字节转换为可读形式
                yearly_size_gb = yearly_size_bytes / (1024**3)  # 转换为GB
                
                print(f"\n估计存储1年数据所需空间: {yearly_size_gb:.2f} GB (基于当前每日数据量)")
            except Exception as e:
                print(f"计算年度存储需求时出错: {e}")
        
        return {
            "db_size": db_size,
            "tables": tables_size,
            "avg_row_size": avg_row_size,
            "daily_stats": daily_stats
        }
    
    except Exception as e:
        print(f"查询数据库大小失败: {e}")
        traceback.print_exc()
        return None


if __name__ == "__main__":
    query_database_size()
