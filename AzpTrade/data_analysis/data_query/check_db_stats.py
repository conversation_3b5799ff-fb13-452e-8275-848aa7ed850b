# -*- coding: utf-8 -*-
import psycopg2
import datetime

# 数据库连接参数
conn_params = {
    "dbname": "stock_data",
    "user": "postgres",
    "password": "MQH1ylhedpia7oM5",
    "host": "localhost",
    "port": "5432"
}

def check_db_stats():
    try:
        # 连接数据库
        conn = psycopg2.connect(**conn_params)
        cur = conn.cursor()
        
        # 1. 查询表大小和行数
        print("===== 表信息 =====")
        cur.execute("""
        SELECT 
            relname AS 表名, 
            n_live_tup AS 行数, 
            pg_size_pretty(pg_relation_size(relid)) AS 表大小
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public' 
        ORDER BY pg_relation_size(relid) DESC;
        """)
        
        tables = cur.fetchall()
        for table in tables:
            print(f"表名: {table[0]}, 行数: {table[1]}, 大小: {table[2]}")
        
        # 2. 查询数据库总大小
        print("\n===== 数据库总大小 =====")
        cur.execute("SELECT pg_size_pretty(pg_database_size('stock_data'));")
        db_size = cur.fetchone()[0]
        print(f"数据库总大小: {db_size}")
        
        # 3. 查询最早和最新的数据日期
        print("\n===== 数据保留时间 =====")
        cur.execute("SELECT MIN(time), MAX(time) FROM stock_prices;")
        min_date, max_date = cur.fetchone()
        
        if min_date and max_date:
            retention_days = (max_date - min_date).days
            print(f"最早数据时间: {min_date}")
            print(f"最新数据时间: {max_date}")
            print(f"数据保留天数: {retention_days} 天")
            
            # 4. 查询每天的数据量
            print("\n===== 每天数据量 =====")
            cur.execute("""
            SELECT 
                DATE(time) AS 日期, 
                COUNT(*) AS 记录数, 
                COUNT(DISTINCT time) AS 时间点数量
            FROM stock_prices 
            GROUP BY DATE(time) 
            ORDER BY DATE(time) DESC 
            LIMIT 10;
            """)
            
            daily_counts = cur.fetchall()
            for day_data in daily_counts:
                print(f"日期: {day_data[0]}, 记录数: {day_data[1]}, 时间点数量: {day_data[2]}")
        
        # 5. 查询磁盘使用情况
        print("\n===== 磁盘使用情况 =====")
        cur.execute("""
        SELECT 
            tablename, 
            pg_size_pretty(pg_total_relation_size(schemaname || '.' || tablename)) AS total_size
        FROM pg_tables
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname || '.' || tablename) DESC;
        """)
        
        disk_usage = cur.fetchall()
        for item in disk_usage:
            print(f"表: {item[0]}, 总大小(含索引): {item[1]}")
        
        # 关闭数据库连接
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"查询数据库统计信息失败: {e}")

if __name__ == "__main__":
    check_db_stats()
