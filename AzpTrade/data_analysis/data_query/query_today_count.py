# -*- coding: utf-8 -*-
import os
import sys
import psycopg2
import datetime
import traceback

# 获取项目根目录并添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))  # AzpTrade目录
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def query_today_data_count():
    """
    查询今天插入到数据库中的数据条数
    
    Returns:
        int: 今天插入的数据条数
    """
    try:
        # 数据库连接参数
        conn_params = {
            "dbname": "stock_data",
            "user": "postgres",
            "password": "MQH1ylhedpia7oM5",  # 数据库密码
            "host": "localhost",
            "port": "5432"
        }
        
        # 连接数据库
        conn = psycopg2.connect(**conn_params)
        cur = conn.cursor()
        
        # 获取今天的日期范围
        today = datetime.datetime.now().date()
        today_start = datetime.datetime.combine(today, datetime.time.min)
        today_end = datetime.datetime.combine(today, datetime.time.max)
        
        # 查询今天的数据量
        query = """
        SELECT COUNT(*) FROM stock_prices 
        WHERE time >= %s AND time <= %s;
        """
        
        # 执行查询
        cur.execute(query, (today_start, today_end))
        count = cur.fetchone()[0]
        
        # 查询不同时间点的数量
        query_time_points = """
        SELECT time, COUNT(*) 
        FROM stock_prices 
        WHERE time >= %s AND time <= %s 
        GROUP BY time 
        ORDER BY time;
        """
        
        cur.execute(query_time_points, (today_start, today_end))
        time_points = cur.fetchall()
        
        # 关闭数据库连接
        cur.close()
        conn.close()
        
        # 打印结果
        print(f"今天({today})共插入了 {count} 条数据")
        print("\n各时间点数据分布:")
        for time_point, point_count in time_points:
            print(f"{time_point.strftime('%Y-%m-%d %H:%M:%S')}: {point_count} 条数据")
        
        return count
    
    except Exception as e:
        print(f"查询数据失败: {e}")
        traceback.print_exc()
        return 0


def query_data_count_by_date(query_date=None):
    """
    查询指定日期插入到数据库中的数据条数
    
    Args:
        query_date: 要查询的日期，格式为'YYYY-MM-DD'，如果为None则查询今天
    
    Returns:
        int: 指定日期插入的数据条数
    """
    try:
        # 解析日期
        if query_date:
            try:
                date_obj = datetime.datetime.strptime(query_date, '%Y-%m-%d').date()
            except ValueError:
                print(f"日期格式错误，应为'YYYY-MM-DD'，例如'2025-05-22'")
                return 0
        else:
            date_obj = datetime.datetime.now().date()
        
        # 设置日期范围
        date_start = datetime.datetime.combine(date_obj, datetime.time.min)
        date_end = datetime.datetime.combine(date_obj, datetime.time.max)
        
        # 数据库连接参数
        conn_params = {
            "dbname": "stock_data",
            "user": "postgres",
            "password": "MQH1ylhedpia7oM5",  # 数据库密码
            "host": "localhost",
            "port": "5432"
        }
        
        # 连接数据库
        conn = psycopg2.connect(**conn_params)
        cur = conn.cursor()
        
        # 查询指定日期的数据量
        query = """
        SELECT COUNT(*) FROM stock_prices 
        WHERE time >= %s AND time <= %s;
        """
        
        # 执行查询
        cur.execute(query, (date_start, date_end))
        count = cur.fetchone()[0]
        
        # 查询不同时间点的数量
        query_time_points = """
        SELECT time, COUNT(*) 
        FROM stock_prices 
        WHERE time >= %s AND time <= %s 
        GROUP BY time 
        ORDER BY time;
        """
        
        cur.execute(query_time_points, (date_start, date_end))
        time_points = cur.fetchall()
        
        # 关闭数据库连接
        cur.close()
        conn.close()
        
        # 打印结果
        print(f"{date_obj}共有 {count} 条数据")
        print("\n各时间点数据分布:")
        for time_point, point_count in time_points:
            print(f"{time_point.strftime('%Y-%m-%d %H:%M:%S')}: {point_count} 条数据")
        
        return count
    
    except Exception as e:
        print(f"查询数据失败: {e}")
        traceback.print_exc()
        return 0


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="查询数据库中的数据插入量")
    parser.add_argument("--date", help="要查询的日期，格式为YYYY-MM-DD，默认为今天")
    
    args = parser.parse_args()
    
    if args.date:
        query_data_count_by_date(args.date)
    else:
        query_today_data_count()
