# -*- coding: utf-8 -*-
import os
import sys
import pandas as pd
import psycopg2
import datetime
import traceback

# 检查是否安装了prettytable，如果没有则尝试安装
try:
    from prettytable import PrettyTable
except ImportError:
    print("正在安装prettytable库...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "prettytable"])
    from prettytable import PrettyTable

# 获取项目根目录并添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))  # AzpTrade目录
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def format_size(size_bytes):
    """将字节数转换为可读格式"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0 or unit == 'TB':
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0

def query_database_stats():
    """查询数据库的统计信息，包括数据时间范围、数据条数和数据库大小"""
    try:
        # 数据库连接参数
        conn_params = {
            "dbname": "stock_data",
            "user": "postgres",
            "password": "MQH1ylhedpia7oM5",  # 数据库密码
            "host": "localhost",
            "port": "5432"
        }
        
        # 连接数据库
        conn = psycopg2.connect(**conn_params)
        cur = conn.cursor()
        
        print("正在查询数据库统计信息...\n")
        
        # 1. 查询表列表
        cur.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
        """)
        tables = [row[0] for row in cur.fetchall()]
        
        if not tables:
            print("数据库中没有表")
            return
        
        # 创建统计信息表格
        stats_table = PrettyTable()
        stats_table.field_names = ["表名", "记录数", "最早数据时间", "最新数据时间", "表大小"]
        
        # 总数据量统计
        total_records = 0
        earliest_time = None
        latest_time = None
        
        # 2. 查询每个表的统计信息
        for table in tables:
            # 检查表是否有time字段
            cur.execute(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '{table}' AND column_name = 'time';
            """)
            has_time = cur.fetchone() is not None
            
            # 查询记录数
            cur.execute(f"SELECT COUNT(*) FROM {table};")
            count = cur.fetchone()[0]
            total_records += count
            
            # 查询时间范围（如果有time字段）
            if has_time:
                cur.execute(f"SELECT MIN(time), MAX(time) FROM {table};")
                min_time, max_time = cur.fetchone()
                
                # 更新全局最早和最晚时间
                if min_time:
                    if earliest_time is None or min_time < earliest_time:
                        earliest_time = min_time
                if max_time:
                    if latest_time is None or max_time > latest_time:
                        latest_time = max_time
            else:
                min_time = "无时间字段"
                max_time = "无时间字段"
            
            # 查询表大小
            cur.execute(f"""
            SELECT pg_size_pretty(pg_total_relation_size('{table}')) as size,
                   pg_total_relation_size('{table}') as raw_size
            """)
            size_pretty, size_raw = cur.fetchone()
            
            # 添加到表格
            stats_table.add_row([table, f"{count:,}", min_time, max_time, size_pretty])
        
        # 3. 查询数据库总大小
        cur.execute("""
        SELECT pg_size_pretty(pg_database_size(current_database())) as size,
               pg_database_size(current_database()) as raw_size
        """)
        db_size_pretty, db_size_raw = cur.fetchone()
        
        # 关闭数据库连接
        cur.close()
        conn.close()
        
        # 输出结果
        print("===== 数据库表统计信息 =====")
        print(stats_table)
        print("\n===== 数据库整体统计信息 =====")
        print(f"总表数: {len(tables)}")
        print(f"总记录数: {total_records:,}")
        if earliest_time and latest_time:
            print(f"数据时间范围: {earliest_time} 至 {latest_time}")
            days = (latest_time - earliest_time).days
            print(f"数据跨度: {days} 天")
        print(f"数据库总大小: {db_size_pretty}")
        
        # 导出结果到CSV文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(current_dir, f"db_stats_{timestamp}.csv")
        
        # 将表格数据转换为DataFrame
        data = []
        for row in stats_table._rows:
            data.append(dict(zip(stats_table.field_names, row)))
        
        df = pd.DataFrame(data)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n统计结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"查询数据库统计信息失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    query_database_stats()
