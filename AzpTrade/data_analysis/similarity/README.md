# 股票分析交易系统

基于板块和概念相似性分析的股票交易推荐系统。该系统通过分析股票在板块和概念中的相似度，挖掘具有投资价值的股票，并提供交易建议。

## 功能特点

- **板块相似性分析**：分析股票在行业板块中的相似度表现
- **概念相似性分析**：分析股票在热门概念中的相似度表现
- **综合评分机制**：多维度评估股票投资价值
- **智能推荐算法**：根据评分和相似度推荐具有投资价值的股票
- **可视化分析报告**：生成直观的图表和HTML格式的分析报告
- **投资组合构建**：根据推荐结果构建合理的投资组合

## 系统架构

系统由以下几个核心模块组成：

1. **数据加载模块**：负责从CSV文件或其他数据源加载股票相似性数据
2. **数据处理模块**：对原始数据进行清洗和预处理
3. **分析引擎**：分析股票相似性，计算评分，挖掘潜在交易机会
4. **交易策略**：根据分析结果生成具体交易建议
5. **可视化模块**：将分析结果可视化，生成直观的图表和报告

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行系统

```bash
python main.py --board_file path/to/board_file.csv --concept_file path/to/concept_file.csv
```

### 参数说明

- `--board_file`：板块相似性数据文件路径
- `--concept_file`：概念相似性数据文件路径
- `--similarity_threshold`：相似性阈值，默认为85.0
- `--top_n`：每个板块/概念选取的前N只股票，默认为10
- `--output_dir`：输出目录，默认为output

## 输出结果

系统会在指定的输出目录生成以下内容：

1. **顶级股票对比图**：展示顶级推荐股票的各项评分对比
2. **股票-板块/概念相似度热图**：直观展示股票与板块/概念的相似度关系
3. **推荐股票评分图**：展示推荐股票的评分和交易建议
4. **推荐原因分布图**：展示推荐原因的分布情况
5. **投资组合配置图**：展示推荐的投资组合配置
6. **HTML格式的投资组合报告**：包含详细的投资建议和图表

## 交易建议解读

系统根据评分和相似度将股票分为以下几类：

- **强烈推荐**：推荐评分 >= 0.75，建议配置20-30%
- **建议买入**：推荐评分 >= 0.6，建议配置10-20%
- **观望**：推荐评分 >= 0.5，建议配置5-10%
- **不推荐**：推荐评分 < 0.5，不建议配置

## 风险提示

本系统提供的交易建议仅供参考，投资者应根据自身风险承受能力和市场情况做出决策。所有投资操作风险自担。

## 开发者

- 电子邮件：<EMAIL>
- 项目地址：https://github.com/yourusername/stock-analysis-system 