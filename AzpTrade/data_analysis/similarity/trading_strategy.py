#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易策略模块
基于分析结果生成具体交易建议
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingStrategy:
    """交易策略类，生成具体交易建议"""
    
    def __init__(self, max_stocks=5, risk_level='medium'):
        """
        初始化交易策略
        
        Args:
            max_stocks: 推荐的最大股票数量
            risk_level: 风险等级，可选值为 'low', 'medium', 'high'
        """
        self.logger = logger
        self.max_stocks = max_stocks
        self.risk_level = risk_level
        
        # 风险等级对应的权重配置
        self.risk_weights = {
            'low': {'score': 0.3, 'similarity': 0.5, 'stability': 0.2},
            'medium': {'score': 0.5, 'similarity': 0.3, 'stability': 0.2},
            'high': {'score': 0.7, 'similarity': 0.2, 'stability': 0.1}
        }
        
    def generate_recommendations(self, analysis_result):
        """
        根据分析结果生成交易建议
        
        Args:
            analysis_result: 分析引擎生成的分析结果
            
        Returns:
            list: 交易建议列表
        """
        self.logger.info(f"开始生成交易建议，风险等级: {self.risk_level}")
        
        top_stocks = analysis_result['top_stocks']
        
        # 为每只股票添加稳定性分数（模拟数据）
        for stock in top_stocks:
            # 稳定性分数是根据多个板块和概念的覆盖程度计算的
            coverage = (stock['board_count'] + stock['concept_count']) / 5  # 假设最大覆盖为5
            stock['stability'] = min(1.0, coverage) * 100
            
            # 根据风险偏好计算综合推荐分数
            weights = self.risk_weights[self.risk_level]
            stock['recommendation_score'] = (
                weights['score'] * stock['final_score'] / 100 +
                weights['similarity'] * stock['normalized_score'] / 100 +
                weights['stability'] * stock['stability'] / 100
            )
            
            # 决定建议操作类型
            if stock['recommendation_score'] >= 0.75:
                stock['action'] = '强烈推荐'
                stock['position_size'] = '20-30%'
            elif stock['recommendation_score'] >= 0.6:
                stock['action'] = '建议买入'
                stock['position_size'] = '10-20%'
            elif stock['recommendation_score'] >= 0.5:
                stock['action'] = '观望'
                stock['position_size'] = '5-10%'
            else:
                stock['action'] = '不推荐'
                stock['position_size'] = '0%'
                
            # 添加建议理由
            reasons = []
            if stock['board_count'] >= 2:
                reasons.append(f"在{stock['board_count']}个板块中表现突出")
            if stock['concept_count'] >= 2:
                reasons.append(f"覆盖{stock['concept_count']}个热门概念")
            if stock['normalized_score'] >= 85:
                reasons.append("相似度评分极高")
                
            stock['reasons'] = reasons if reasons else ["综合评分良好"]
            
            # 添加预计风险等级
            if stock['stability'] >= 80:
                stock['risk_level'] = '低风险'
            elif stock['stability'] >= 60:
                stock['risk_level'] = '中等风险'
            else:
                stock['risk_level'] = '高风险'
                
        # 按推荐分数排序并选取前N只股票
        recommendations = sorted(
            top_stocks, 
            key=lambda x: x['recommendation_score'], 
            reverse=True
        )[:self.max_stocks]
        
        # 添加时间戳
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        result = {
            'timestamp': timestamp,
            'risk_level': self.risk_level,
            'stocks': recommendations
        }
        
        self.logger.info(f"交易建议生成完成，共推荐 {len(recommendations)} 只股票")
        return result
        
    def build_portfolio(self, recommendations):
        """
        根据推荐构建投资组合
        
        Args:
            recommendations: 生成的推荐股票列表
            
        Returns:
            dict: 投资组合配置
        """
        self.logger.info("开始构建投资组合")
        
        stocks = recommendations['stocks']
        
        # 计算总权重
        total_weight = sum(float(s['position_size'].split('-')[1].replace('%', '')) / 100 
                          for s in stocks 
                          if s['action'] != '不推荐')
        
        # 归一化权重
        portfolio = []
        for stock in stocks:
            if stock['action'] == '不推荐':
                continue
                
            max_position = float(stock['position_size'].split('-')[1].replace('%', '')) / 100
            normalized_weight = max_position / total_weight if total_weight > 0 else 0
            
            portfolio.append({
                'stock_id': stock['stock_id'],
                'stock_name': stock['stock_name'],
                'weight': normalized_weight * 100,  # 转为百分比
                'action': stock['action'],
                'risk_level': stock['risk_level']
            })
            
        result = {
            'timestamp': recommendations['timestamp'],
            'risk_level': recommendations['risk_level'],
            'stocks': portfolio,
            'total_allocation': sum(s['weight'] for s in portfolio)
        }
        
        self.logger.info(f"投资组合构建完成，包含 {len(portfolio)} 只股票")
        return result 