#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
可视化模块
将分析结果和交易建议可视化展示
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import seaborn as sns
import logging
import sys

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Visualizer:
    """可视化类，负责生成图表和报告"""
    
    def __init__(self, output_dir='output', figsize=(12, 8)):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出目录
            figsize: 图表大小
        """
        self.logger = logger
        self.output_dir = output_dir
        self.figsize = figsize
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置中文字体
        self._set_chinese_font()
        
        # 设置Seaborn样式
        sns.set(style="whitegrid")
        
    def _set_chinese_font(self):
        """设置中文字体"""
        try:
            # 导入字体管理模块
            import matplotlib.font_manager as fm
            from matplotlib.font_manager import FontProperties
            
            # 检查系统中可用的中文字体
            self.logger.info("正在检查系统中可用的中文字体...")
            
            # 根据系统类型设置字体
            if sys.platform.startswith('darwin'):  # macOS
                # macOS上常见的中文字体
                chinese_fonts = [
                    'PingFang SC', 'Heiti SC', 'Songti SC', 'STHeiti', 
                    'Hiragino Sans GB', 'Yuanti SC', 'Kaiti SC'
                ]
                # 字体文件路径
                font_files = [
                    '/System/Library/Fonts/PingFang.ttc',
                    '/System/Library/Fonts/STHeiti Light.ttc',
                    '/System/Library/Fonts/STHeiti Medium.ttc',
                    '/System/Library/Fonts/Hiragino Sans GB.ttc',
                    '/System/Library/Fonts/Supplemental/Songti.ttc'
                ]
            elif sys.platform.startswith('win'):  # Windows
                # Windows上常见的中文字体
                chinese_fonts = [
                    'Microsoft YaHei', 'SimHei', 'SimSun', 'FangSong', 'KaiTi'
                ]
                # 字体文件路径
                font_files = [
                    'C:/Windows/Fonts/simhei.ttf',
                    'C:/Windows/Fonts/msyh.ttf',
                    'C:/Windows/Fonts/simsun.ttc'
                ]
            else:  # Linux或其他
                # Linux上常见的中文字体
                chinese_fonts = [
                    'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'Noto Sans CJK TC'
                ]
                # 字体文件路径
                font_files = [
                    '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
                    '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc'
                ]
            
            # 获取系统中实际可用的字体
            available_fonts = set(f.name for f in fm.fontManager.ttflist)
            self.logger.info(f"系统中检测到的字体数量: {len(available_fonts)}")
            
            # 找出可用的中文字体
            usable_chinese_fonts = [font for font in chinese_fonts if font in available_fonts]
            
            # 创建字体属性对象
            self.chinese_font_prop = None
            
            # 首先尝试使用系统中已知的中文字体名称
            if usable_chinese_fonts:
                self.logger.info(f"找到可用的中文字体: {', '.join(usable_chinese_fonts)}")
                self.chinese_font_prop = FontProperties(family=usable_chinese_fonts[0])
                self.logger.info(f"使用字体: {usable_chinese_fonts[0]}")
            else:
                # 如果没有找到指定的中文字体，尝试直接使用字体文件
                self.logger.warning("未找到指定的中文字体，尝试使用字体文件")
                
                for font_file in font_files:
                    if os.path.exists(font_file):
                        try:
                            self.chinese_font_prop = FontProperties(fname=font_file)
                            self.logger.info(f"使用字体文件: {font_file}")
                            break
                        except Exception as e:
                            self.logger.warning(f"加载字体文件失败 {font_file}: {str(e)}")
            
            # 如果仍然没有找到可用的字体，尝试使用系统上可能的中文字体
            if self.chinese_font_prop is None:
                potential_chinese_fonts = [f for f in available_fonts if any(keyword in f for keyword in 
                                                                           ['Ping', 'Hei', 'Song', 'Yuan', 'Kai', 'Micro', 'Noto', 'CJK', 'SC', 'TC', 'GB'])]
                if potential_chinese_fonts:
                    self.logger.info(f"找到潜在的中文字体: {', '.join(potential_chinese_fonts[:5])}...等")
                    self.chinese_font_prop = FontProperties(family=potential_chinese_fonts[0])
                    self.logger.info(f"使用潜在字体: {potential_chinese_fonts[0]}")
                else:
                    self.logger.warning("未找到任何可能的中文字体，图表中的中文可能显示为乱码")
                    # 使用默认字体
                    self.chinese_font_prop = FontProperties()
            
            # 设置全局字体参数
            plt.rcParams['axes.unicode_minus'] = False
            
        except Exception as e:
            self.logger.error(f"设置中文字体时出错: {str(e)}")
            # 使用默认字体
            self.chinese_font_prop = FontProperties()
            
    def plot_top_stocks(self, analysis_result):
        """
        绘制顶级股票分析图
        
        Args:
            analysis_result: 分析引擎的分析结果
        """
        self.logger.info("开始绘制顶级股票分析图")
        
        top_stocks = analysis_result['top_stocks']
        
        # 转换为DataFrame，方便绘图
        df = pd.DataFrame(top_stocks)
        
        # 绘制评分对比图
        plt.figure(figsize=self.figsize)
        
        # 创建股票名和股票代码的标签
        labels = [f"{row['stock_name']}({row['stock_id']})" for _, row in df.iterrows()]
        
        # 绘制条形图
        x = range(len(df))
        bar_width = 0.25
        
        plt.bar(x, df['final_score'], width=bar_width, label='最终评分', color='#1f77b4')
        plt.bar([i + bar_width for i in x], df['normalized_score'], width=bar_width, label='标准化评分', color='#ff7f0e')
        plt.bar([i + 2*bar_width for i in x], df['stability'], width=bar_width, label='稳定性', color='#2ca02c')
        
        plt.xlabel('股票', fontproperties=self.chinese_font_prop)
        plt.ylabel('评分', fontproperties=self.chinese_font_prop)
        plt.title('顶级推荐股票评分对比', fontproperties=self.chinese_font_prop)
        plt.xticks([i + bar_width for i in x], labels, rotation=45, ha='right', fontproperties=self.chinese_font_prop)
        plt.legend(prop=self.chinese_font_prop)
        plt.tight_layout()
        
        # 保存图表
        plt.savefig(os.path.join(self.output_dir, 'top_stocks_comparison.png'), dpi=300)
        
        # 绘制板块/概念覆盖热图
        plt.figure(figsize=self.figsize)
        
        # 准备热图数据
        heatmap_data = []
        for stock in top_stocks[:10]:  # 只取前10只股票
            for detail in stock['details']:
                heatmap_data.append({
                    'stock_name': f"{stock['stock_name']}({stock['stock_id']})",
                    'category': f"{detail['name']}({detail['type']})",
                    'similarity': detail['similarity']
                })
                
        if heatmap_data:
            heatmap_df = pd.DataFrame(heatmap_data)
            heatmap_pivot = heatmap_df.pivot_table(
                index='stock_name', 
                columns='category', 
                values='similarity',
                aggfunc='mean'
            ).fillna(0)
            
            # 绘制热图
            sns.set(font=self.chinese_font_prop.get_name())
            ax = sns.heatmap(heatmap_pivot, annot=True, cmap='YlGnBu', fmt='.1f')
            
            plt.title('股票-板块/概念相似度热图', fontproperties=self.chinese_font_prop)
            plt.xlabel('', fontproperties=self.chinese_font_prop)
            plt.ylabel('', fontproperties=self.chinese_font_prop)
            
            # 设置热图的x轴和y轴的字体
            for label in ax.get_xticklabels():
                label.set_fontproperties(self.chinese_font_prop)
            for label in ax.get_yticklabels():
                label.set_fontproperties(self.chinese_font_prop)
                
            plt.tight_layout()
            
            # 保存热图
            plt.savefig(os.path.join(self.output_dir, 'stock_category_heatmap.png'), dpi=300)
        
        self.logger.info("顶级股票分析图绘制完成")
        
    def plot_recommendations(self, recommendations):
        """
        绘制推荐股票图表
        
        Args:
            recommendations: 交易策略生成的推荐结果
        """
        self.logger.info("开始绘制推荐股票图表")
        
        stocks = recommendations['stocks']
        
        # 转换为DataFrame
        df = pd.DataFrame(stocks)
        
        if df.empty:
            self.logger.warning("没有推荐的股票，无法绘制图表")
            return
            
        # 根据推荐评分排序
        df = df.sort_values('recommendation_score', ascending=False)
        
        # 绘制推荐评分图
        plt.figure(figsize=self.figsize)
        
        # 创建标签
        labels = [f"{row['stock_name']}({row['stock_id']})" for _, row in df.iterrows()]
        
        # 根据推荐行动设置颜色
        norm = plt.Normalize(df['recommendation_score'].min(), df['recommendation_score'].max())
        colors = plt.cm.viridis(norm(df['recommendation_score']))
        
        # 绘制水平条形图
        ax = plt.gca()
        ax.barh(labels, df['recommendation_score'], color=colors)
        plt.xlabel('推荐评分', fontproperties=self.chinese_font_prop)
        plt.ylabel('股票', fontproperties=self.chinese_font_prop)
        plt.title('推荐股票评分', fontproperties=self.chinese_font_prop)
        
        # 设置x轴和y轴的字体
        for label in ax.get_xticklabels():
            label.set_fontproperties(self.chinese_font_prop)
        for label in ax.get_yticklabels():
            label.set_fontproperties(self.chinese_font_prop)
            
        plt.tight_layout()
        
        # 添加颜色图例
        sm = plt.cm.ScalarMappable(cmap=plt.cm.viridis, norm=norm)
        sm.set_array([])
        cbar = plt.colorbar(sm, ax=ax)  # 明确指定轴
        cbar.set_label('推荐强度', fontproperties=self.chinese_font_prop)
        
        # 保存图表
        plt.savefig(os.path.join(self.output_dir, 'recommendation_scores.png'), dpi=300)
        
        # 绘制推荐原因分布
        plt.figure(figsize=self.figsize)
        
        # 统计各种推荐原因的出现次数
        all_reasons = []
        for stock in stocks:
            all_reasons.extend(stock['reasons'])
            
        reason_counts = pd.Series(all_reasons).value_counts()
        
        # 绘制饼图
        text_props = {'fontproperties': self.chinese_font_prop}
        plt.pie(reason_counts, labels=reason_counts.index, autopct='%1.1f%%', startangle=90, textprops=text_props)
        plt.axis('equal')
        plt.title('推荐原因分布', fontproperties=self.chinese_font_prop)
        
        # 保存图表
        plt.savefig(os.path.join(self.output_dir, 'recommendation_reasons.png'), dpi=300)
        
        self.logger.info("推荐股票图表绘制完成")
        
    def generate_report(self, portfolio):
        """
        生成投资组合报告
        
        Args:
            portfolio: 投资组合配置
        """
        self.logger.info("开始生成投资组合报告")
        
        # 绘制投资组合分配图
        plt.figure(figsize=self.figsize)
        
        stocks = portfolio['stocks']
        
        if not stocks:
            self.logger.warning("投资组合为空，无法生成报告")
            return
            
        # 准备数据
        labels = [f"{stock['stock_name']}({stock['stock_id']})" for stock in stocks]
        sizes = [stock['weight'] for stock in stocks]
        
        # 设置颜色
        colors = plt.cm.Paired(np.linspace(0, 1, len(labels)))
        
        # 创建文本属性对象，用于标签
        text_props = {'fontproperties': self.chinese_font_prop}
        
        # 绘制饼图
        plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90, colors=colors, 
                textprops=text_props, wedgeprops={'linewidth': 0.5, 'edgecolor': 'white'})
        plt.axis('equal')
        plt.title(f"投资组合配置 (风险等级: {portfolio['risk_level']})", fontproperties=self.chinese_font_prop)
        
        # 保存图表
        plt.savefig(os.path.join(self.output_dir, 'portfolio_allocation.png'), dpi=300)
        
        # 生成HTML报告
        html_report = self._generate_html_report(portfolio)
        
        # 保存HTML报告
        with open(os.path.join(self.output_dir, 'portfolio_report.html'), 'w', encoding='utf-8') as f:
            f.write(html_report)
            
        self.logger.info("投资组合报告生成完成")
        
    def _generate_html_report(self, portfolio):
        """
        生成HTML格式的投资组合报告
        
        Args:
            portfolio: 投资组合配置
            
        Returns:
            str: HTML报告内容
        """
        stocks = portfolio['stocks']
        
        # 生成股票表格内容
        stock_rows = ""
        for stock in stocks:
            stock_rows += f"""
            <tr>
                <td>{stock['stock_id']}</td>
                <td>{stock['stock_name']}</td>
                <td>{stock['weight']:.2f}%</td>
                <td>{stock['action']}</td>
                <td>{stock['risk_level']}</td>
            </tr>
            """
            
        # 生成HTML报告
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>投资组合报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2 {{ color: #2c3e50; }}
                table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #3498db; color: white; }}
                tr:hover {{ background-color: #f5f5f5; }}
                .info {{ background-color: #eaf2f8; padding: 15px; border-radius: 5px; margin-top: 20px; }}
                .charts {{ display: flex; flex-wrap: wrap; justify-content: space-around; margin-top: 20px; }}
                .chart {{ margin: 10px; text-align: center; }}
                img {{ max-width: 100%; height: auto; }}
            </style>
        </head>
        <body>
            <h1>投资组合报告</h1>
            
            <div class="info">
                <p><strong>生成时间:</strong> {portfolio['timestamp']}</p>
                <p><strong>风险等级:</strong> {portfolio['risk_level']}</p>
                <p><strong>总配置比例:</strong> {portfolio['total_allocation']:.2f}%</p>
            </div>
            
            <h2>推荐股票组合</h2>
            <table>
                <tr>
                    <th>股票代码</th>
                    <th>股票名称</th>
                    <th>配置比例</th>
                    <th>交易建议</th>
                    <th>风险等级</th>
                </tr>
                {stock_rows}
            </table>
            
            <div class="charts">
                <div class="chart">
                    <h2>投资组合配置</h2>
                    <img src="portfolio_allocation.png" alt="投资组合配置">
                </div>
                <div class="chart">
                    <h2>推荐股票评分</h2>
                    <img src="recommendation_scores.png" alt="推荐股票评分">
                </div>
            </div>
            
            <div class="charts">
                <div class="chart">
                    <h2>顶级股票评分对比</h2>
                    <img src="top_stocks_comparison.png" alt="顶级股票评分对比">
                </div>
                <div class="chart">
                    <h2>推荐原因分布</h2>
                    <img src="recommendation_reasons.png" alt="推荐原因分布">
                </div>
            </div>
            
            <div class="info">
                <h2>投资建议</h2>
                <p>根据板块和概念相似性分析，上述股票在所属板块和概念中表现突出，具有较高的投资价值。</p>
                <p>建议投资者根据自身风险承受能力，参考交易建议进行合理配置。</p>
            </div>
            
        </body>
        </html>
        """
        
        return html 