#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
股票相似度分析器
该模块用于分析股票与板块、概念的相似度数据，提取高相似度的股票
"""

import os
import sys
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import pandas as pd

# 确保能正确导入项目模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入项目相关模块
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix
import trade.log.YfyLog as yfylog


class StockSimilarityAnalyzer:
    """股票相似度分析器
    用于分析Redis中存储的股票相似度数据，提取高相似度的股票
    """
    
    def __init__(self):
        """初始化分析器"""
        self.redis_mgr = StockRedisMgr()
        self.similarity_prefix = RedisKeyPrefix.INFO_SIMILARITY.value
        
    def _get_all_similarity_data(self) -> Dict[str, Dict]:
        """获取所有股票的相似度数据
        
        Returns:
            Dict[str, Dict]: 股票代码到相似度数据的映射
        """
        # 使用Redis的scan方法获取所有相似度数据的键
        cursor = 0
        all_keys = []
        pattern = f"{self.similarity_prefix}:*"
        
        # 使用scan迭代获取所有匹配的键
        while True:
            cursor, keys = self.redis_mgr._redis.scan(cursor=cursor, match=pattern, count=1000)
            all_keys.extend(keys)
            if cursor == 0:
                break
        
        # 批量获取所有键的值
        if not all_keys:
            yfylog.logger.warning("未找到任何股票相似度数据")
            return {}
            
        data = self.redis_mgr.batch_get(all_keys)
        
        # 提取股票代码作为键
        result = {}
        for key, value in data.items():
            if value and isinstance(value, dict) and 'stock_id' in value:
                result[value['stock_id']] = value
        
        yfylog.logger.info(f"共获取到 {len(result)} 只股票的相似度数据")
        return result
    
    def _group_stocks_by_board(self, similarity_data: Dict[str, Dict]) -> Dict[str, List[Tuple[str, float]]]:
        """按板块分组股票
        
        Args:
            similarity_data: 股票代码到相似度数据的映射
            
        Returns:
            Dict[str, List[Tuple[str, float]]]: 板块ID到(股票代码,相似度)列表的映射
        """
        board_stocks = defaultdict(list)
        
        for stock_id, data in similarity_data.items():
            if 'board' in data and data['board'] and 'id' in data['board'] and 'similarity' in data['board']:
                board_id = data['board']['id']
                similarity = data['board']['similarity']
                board_stocks[board_id].append((stock_id, similarity))
        
        return board_stocks
    
    def _group_stocks_by_concept(self, similarity_data: Dict[str, Dict]) -> Dict[str, List[Tuple[str, float]]]:
        """按概念分组股票
        
        Args:
            similarity_data: 股票代码到相似度数据的映射
            
        Returns:
            Dict[str, List[Tuple[str, float]]]: 概念ID到(股票代码,相似度)列表的映射
        """
        concept_stocks = defaultdict(list)
        
        for stock_id, data in similarity_data.items():
            if 'concepts' in data and data['concepts']:
                for concept in data['concepts']:
                    if 'id' in concept and 'similarity' in concept:
                        concept_id = concept['id']
                        similarity = concept['similarity']
                        concept_stocks[concept_id].append((stock_id, similarity))
        
        return concept_stocks
    
    def get_top_similar_stocks_by_board(self, percentage: float = 0.05) -> Dict[str, List[Dict[str, Any]]]:
        """获取每个板块相似度最高的前N%的股票
        
        Args:
            percentage: 选取的百分比，默认为0.05（5%）
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 板块ID到高相似度股票列表的映射
        """
        # 获取所有股票的相似度数据
        all_data = self._get_all_similarity_data()
        if not all_data:
            return {}
        
        # 按板块分组股票
        board_stocks = self._group_stocks_by_board(all_data)
        
        # 对每个板块的股票按相似度排序，并选取前N%
        result = {}
        for board_id, stocks in board_stocks.items():
            # 按相似度降序排序
            sorted_stocks = sorted(stocks, key=lambda x: x[1], reverse=True)
            
            # 计算要选取的股票数量
            count = max(1, int(len(sorted_stocks) * percentage))
            top_stocks = sorted_stocks[:count]
            
            # 构建结果
            board_name = all_data[top_stocks[0][0]]['board']['name'] if top_stocks else ""
            result[board_id] = {
                'board_id': board_id,
                'board_name': board_name,
                'total_stocks': len(sorted_stocks),
                'selected_stocks': [
                    {
                        'stock_id': stock_id,
                        'stock_name': all_data[stock_id]['stock_name'],
                        'similarity': similarity
                    } for stock_id, similarity in top_stocks
                ]
            }
        
        return result
    
    def get_top_similar_stocks_by_concept(self, percentage: float = 0.05) -> Dict[str, Dict[str, Any]]:
        """获取每个概念相似度最高的前N%的股票
        
        Args:
            percentage: 选取的百分比，默认为0.05（5%）
            
        Returns:
            Dict[str, Dict[str, Any]]: 概念ID到高相似度股票列表的映射
        """
        # 获取所有股票的相似度数据
        all_data = self._get_all_similarity_data()
        if not all_data:
            return {}
        
        # 按概念分组股票
        concept_stocks = self._group_stocks_by_concept(all_data)
        
        # 对每个概念的股票按相似度排序，并选取前N%
        result = {}
        for concept_id, stocks in concept_stocks.items():
            # 按相似度降序排序
            sorted_stocks = sorted(stocks, key=lambda x: x[1], reverse=True)
            
            # 计算要选取的股票数量
            count = max(1, int(len(sorted_stocks) * percentage))
            top_stocks = sorted_stocks[:count]
            
            # 查找概念名称
            concept_name = ""
            for stock_id, _ in top_stocks:
                for concept in all_data[stock_id]['concepts']:
                    if concept['id'] == concept_id:
                        concept_name = concept['name']
                        break
                if concept_name:
                    break
            
            # 构建结果
            result[concept_id] = {
                'concept_id': concept_id,
                'concept_name': concept_name,
                'total_stocks': len(sorted_stocks),
                'selected_stocks': [
                    {
                        'stock_id': stock_id,
                        'stock_name': all_data[stock_id]['stock_name'],
                        'similarity': similarity
                    } for stock_id, similarity in top_stocks
                ]
            }
        
        return result
    
    def export_top_board_stocks_to_csv(self, output_file: str, percentage: float = 0.05) -> bool:
        """将每个板块相似度最高的股票导出到CSV文件
        
        Args:
            output_file: 输出文件路径
            percentage: 选取的百分比，默认为0.05（5%）
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取每个板块相似度最高的股票
            board_results = self.get_top_similar_stocks_by_board(percentage)
            
            # 准备数据
            rows = []
            for board_id, data in board_results.items():
                for stock in data['selected_stocks']:
                    rows.append({
                        'board_id': board_id,
                        'board_name': data['board_name'],
                        'stock_id': stock['stock_id'],
                        'stock_name': stock['stock_name'],
                        'similarity': stock['similarity'],
                        'total_stocks': data['total_stocks']
                    })
            
            # 创建DataFrame并导出到CSV
            if rows:
                df = pd.DataFrame(rows)
                df.to_csv(output_file, index=False, encoding='utf-8-sig')
                yfylog.logger.info(f"板块相似度数据已导出到 {output_file}")
                return True
            else:
                yfylog.logger.warning("没有数据可导出")
                return False
                
        except Exception as e:
            yfylog.logger.error(f"导出板块相似度数据失败: {e}")
            return False
    
    def export_top_concept_stocks_to_csv(self, output_file: str, percentage: float = 0.05) -> bool:
        """将每个概念相似度最高的股票导出到CSV文件
        
        Args:
            output_file: 输出文件路径
            percentage: 选取的百分比，默认为0.05（5%）
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取每个概念相似度最高的股票
            concept_results = self.get_top_similar_stocks_by_concept(percentage)
            
            # 准备数据
            rows = []
            for concept_id, data in concept_results.items():
                for stock in data['selected_stocks']:
                    rows.append({
                        'concept_id': concept_id,
                        'concept_name': data['concept_name'],
                        'stock_id': stock['stock_id'],
                        'stock_name': stock['stock_name'],
                        'similarity': stock['similarity'],
                        'total_stocks': data['total_stocks']
                    })
            
            # 创建DataFrame并导出到CSV
            if rows:
                df = pd.DataFrame(rows)
                df.to_csv(output_file, index=False, encoding='utf-8-sig')
                yfylog.logger.info(f"概念相似度数据已导出到 {output_file}")
                return True
            else:
                yfylog.logger.warning("没有数据可导出")
                return False
                
        except Exception as e:
            yfylog.logger.error(f"导出概念相似度数据失败: {e}")
            return False


def main():
    """主函数，用于测试"""
    analyzer = StockSimilarityAnalyzer()
    
    # 导出板块相似度数据
    board_file = 'top_board_similar_stocks.csv'
    analyzer.export_top_board_stocks_to_csv(board_file)
    
    # 导出概念相似度数据
    concept_file = 'top_concept_similar_stocks.csv'
    analyzer.export_top_concept_stocks_to_csv(concept_file)
    
    print(f"数据已导出到 {board_file} 和 {concept_file}")


if __name__ == "__main__":
    main()
