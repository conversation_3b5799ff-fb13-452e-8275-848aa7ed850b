#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
股票推荐器
基于相似度分析，推荐最值得购买的5-10支股票
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from collections import defaultdict

# 确保能正确导入项目模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入项目相关模块
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix
import trade.log.YfyLog as yfylog
from data_analysis.similarity.StockSimilarityAnalyzer import StockSimilarityAnalyzer
import trade.network.YFYDataMgr as yfydm
from trade.enum.WxMsgType import WxMsgType
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix
import trade.manager.TimeDateMgr as timeDateMgr

class StockRecommender:
    """
    股票推荐器
    基于相似度分析，推荐最值得购买的股票
    """
    
    def __init__(self, max_recommendations=10, similarity_threshold=90.0):
        """
        初始化推荐器
        
        Args:
            max_recommendations: 最大推荐股票数量
            similarity_threshold: 相似度阈值，只考虑高于此阈值的股票
        """
        self.max_recommendations = max_recommendations
        self.similarity_threshold = similarity_threshold
        self.redis_mgr = StockRedisMgr()
        self.analyzer = StockSimilarityAnalyzer()
        
    def _load_similarity_data(self):
        """
        加载相似度数据
        
        Returns:
            tuple: (板块相似度DataFrame, 概念相似度DataFrame)
        """
        # 导出数据到临时CSV文件
        board_file = 'temp_board_similar_stocks.csv'
        concept_file = 'temp_concept_similar_stocks.csv'
        
        self.analyzer.export_top_board_stocks_to_csv(board_file)
        self.analyzer.export_top_concept_stocks_to_csv(concept_file)
        
        # 读取CSV文件
        board_df = pd.read_csv(board_file)
        concept_df = pd.read_csv(concept_file)
        
        # 清理临时文件
        try:
            os.remove(board_file)
            os.remove(concept_file)
        except Exception as e:
            yfylog.logger.warning(f"清理临时文件失败: {e}")
            
        return board_df, concept_df
    
    def _calculate_stock_scores(self, board_df, concept_df):
        """
        计算股票综合评分
        
        Args:
            board_df: 板块相似度DataFrame
            concept_df: 概念相似度DataFrame
            
        Returns:
            dict: 股票评分字典
        """
        # 筛选高相似度数据
        high_board_similarity = board_df[board_df['similarity'] >= self.similarity_threshold]
        high_concept_similarity = concept_df[concept_df['similarity'] >= self.similarity_threshold]
        
        # 用于存储股票评分
        stock_scores = defaultdict(lambda: {
            'score': 0, 
            'board_count': 0, 
            'concept_count': 0, 
            'boards': [], 
            'concepts': [],
            'stock_name': ''
        })
        
        # 处理板块相似度数据
        for _, row in high_board_similarity.iterrows():
            # 确保股票ID为6位数字字符串，保留前导零
            stock_id = str(row['stock_id']).zfill(6)
            stock_scores[stock_id]['score'] += row['similarity'] * 0.6  # 板块权重0.6
            stock_scores[stock_id]['board_count'] += 1
            stock_scores[stock_id]['stock_name'] = row['stock_name']
            stock_scores[stock_id]['boards'].append({
                'name': row['board_name'],
                'similarity': row['similarity']
            })
        
        # 处理概念相似度数据
        for _, row in high_concept_similarity.iterrows():
            # 确保股票ID为6位数字字符串，保留前导零
            stock_id = str(row['stock_id']).zfill(6)
            stock_scores[stock_id]['score'] += row['similarity'] * 0.4  # 概念权重0.4
            stock_scores[stock_id]['concept_count'] += 1
            stock_scores[stock_id]['stock_name'] = row['stock_name']
            stock_scores[stock_id]['concepts'].append({
                'name': row['concept_name'],
                'similarity': row['similarity']
            })
        
        # 计算最终评分
        for stock_id, info in stock_scores.items():
            # 标准化评分
            total_weight = (info['board_count'] * 0.6 + info['concept_count'] * 0.4)
            info['normalized_score'] = info['score'] / total_weight if total_weight > 0 else 0
            
            # 计算板块和概念覆盖因子
            coverage_factor = min(5, info['board_count'] + info['concept_count']) / 5
            
            # 计算多样性因子（同时出现在板块和概念中的股票得分更高）
            diversity_factor = 1.0
            if info['board_count'] > 0 and info['concept_count'] > 0:
                diversity_factor = 1.2
            
            # 最终评分
            info['final_score'] = info['normalized_score'] * coverage_factor * diversity_factor
            
            # 排序板块和概念列表
            info['boards'] = sorted(info['boards'], key=lambda x: x['similarity'], reverse=True)
            info['concepts'] = sorted(info['concepts'], key=lambda x: x['similarity'], reverse=True)
        
        return stock_scores
    
    def _generate_buy_reasons(self, stock_info):
        """
        生成购买理由
        
        Args:
            stock_info: 股票信息字典
            
        Returns:
            list: 购买理由列表
        """
        reasons = []
        
        # 板块相关理由
        if stock_info['board_count'] >= 3:
            top_boards = [b['name'] for b in stock_info['boards'][:3]]
            reasons.append(f"在多个重要板块中表现突出: {', '.join(top_boards)}")
        elif stock_info['board_count'] > 0:
            top_board = stock_info['boards'][0]['name']
            reasons.append(f"在{top_board}板块中表现突出")
        
        # 概念相关理由
        if stock_info['concept_count'] >= 3:
            top_concepts = [c['name'] for c in stock_info['concepts'][:3]]
            reasons.append(f"覆盖多个热门概念: {', '.join(top_concepts)}")
        elif stock_info['concept_count'] > 0:
            top_concept = stock_info['concepts'][0]['name']
            reasons.append(f"在{top_concept}概念中表现突出")
        
        # 相似度评分理由
        if stock_info['normalized_score'] >= 95:
            reasons.append("相似度评分极高")
        elif stock_info['normalized_score'] >= 90:
            reasons.append("相似度评分很高")
        
        # 多样性理由
        if stock_info['board_count'] > 0 and stock_info['concept_count'] > 0:
            reasons.append("同时在板块和概念中表现优异，多元化程度高")
        
        return reasons
    
    def _determine_action(self, score):
        """
        根据评分确定推荐操作
        
        Args:
            score: 最终评分
            
        Returns:
            tuple: (操作建议, 仓位建议)
        """
        if score >= 95:
            return "强烈推荐买入", "20-30%"
        elif score >= 90:
            return "建议买入", "15-20%"
        elif score >= 85:
            return "可以考虑", "10-15%"
        elif score >= 80:
            return "观望", "5-10%"
        else:
            return "暂不推荐", "0%"
    
    def get_top_recommendations(self):
        """
        获取最值得购买的股票推荐
        
        Returns:
            list: 推荐股票列表
        """
        yfylog.logger.info(f"开始生成股票推荐，最大推荐数量: {self.max_recommendations}")
        
        # 加载相似度数据
        board_df, concept_df = self._load_similarity_data()
        
        # 计算股票评分
        stock_scores = self._calculate_stock_scores(board_df, concept_df)
        
        # 按最终评分排序
        sorted_stocks = sorted(
            stock_scores.items(), 
            key=lambda x: x[1]['final_score'], 
            reverse=True
        )
        
        # 生成推荐列表
        recommendations = []
        for stock_id, info in sorted_stocks[:self.max_recommendations]:
            # 确定操作建议和仓位
            action, position = self._determine_action(info['final_score'])
            
            # 生成购买理由
            reasons = self._generate_buy_reasons(info)
            
            # 构建推荐信息
            recommendation = {
                'stock_id': stock_id,
                'stock_name': info['stock_name'],
                'final_score': round(info['final_score'], 2),
                'normalized_score': round(info['normalized_score'], 2),
                'board_count': info['board_count'],
                'concept_count': info['concept_count'],
                'action': action,
                'position': position,
                'reasons': reasons,
                'top_boards': [b['name'] for b in info['boards'][:3]] if info['boards'] else [],
                'top_concepts': [c['name'] for c in info['concepts'][:3]] if info['concepts'] else []
            }
            
            recommendations.append(recommendation)
        
        yfylog.logger.info(f"股票推荐生成完成，共推荐 {len(recommendations)} 只股票")
        return recommendations
    
    def export_recommendations(self, file_path='../../top_stock_recommendations.csv'):
        """
        将推荐结果导出到CSV文件
        
        Args:
            file_path (str): CSV文件路径
        """
        recommendations = self.get_top_recommendations()
        
        # 准备CSV数据
        data = []
        for rec in recommendations:
            # 确保股票ID为6位数字字符串，保留前导零
            stock_id = str(rec['stock_id']).zfill(6)
            
            reasons = '; '.join(rec['reasons'])
            top_boards = '; '.join(rec['top_boards']) if rec['top_boards'] else ''
            top_concepts = '; '.join(rec['top_concepts']) if rec['top_concepts'] else ''
            
            data.append({
                'stock_id': stock_id,  # 使用格式化后的6位股票ID
                'stock_name': rec['stock_name'],
                'score': rec['final_score'],
                'action': rec['action'],
                'position': rec['position'],
                'board_count': rec['board_count'],
                'concept_count': rec['concept_count'],
                'reasons': reasons,
                'top_boards': top_boards,
                'top_concepts': top_concepts,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # 创建DataFrame并导出到CSV
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False)
        
        yfylog.logger.info(f"推荐结果已导出到 {file_path}")
        print(f"推荐结果已保存到 {file_path}")
    
    def print_recommendations(self):
        """
        打印推荐结果
        """
        recommendations = self.get_top_recommendations()
        
        print("\n" + "=" * 80)
        print(f"股票推荐结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        for i, rec in enumerate(recommendations, 1):
            # 确保股票ID为6位数字字符串，保留前导零
            stock_id = str(rec['stock_id']).zfill(6)
            rec['stock_id'] = stock_id  # 更新推荐对象中的股票ID
            
            print(f"\n{i}. {rec['stock_name']}({stock_id}) - {rec['action']}")
            print(f"   评分: {rec['final_score']:.2f}  推荐仓位: {rec['position']}")
            print(f"   板块数: {rec['board_count']}  概念数: {rec['concept_count']}")
            
            if rec['top_boards']:
                print(f"   主要板块: {', '.join(rec['top_boards'])}")
            if rec['top_concepts']:
                print(f"   主要概念: {', '.join(rec['top_concepts'])}")
                
            print("   推荐理由:")
            for reason in rec['reasons']:
                print(f"   - {reason}")
        
        print("\n" + "=" * 80)


def run():
    """
    主函数
    """
    if not timeDateMgr.is_trade_day():
        return

    # 创建推荐器实例
    recommender = StockRecommender(max_recommendations=10, similarity_threshold=85.0)
    
    # 只获取一次推荐结果
    yfylog.logger.info("开始生成股票推荐，最大推荐数量: 10")
    recommendations = recommender.get_top_recommendations()
    yfylog.logger.info(f"股票推荐生成完成，共推荐 {len(recommendations)} 只股票")
    
    # 打印详细推荐结果
    print("\n" + "=" * 80)
    print(f"股票推荐结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    for i, rec in enumerate(recommendations, 1):
        # 确保股票ID为6位数字字符串，保留前导零
        stock_id = str(rec['stock_id']).zfill(6)
        rec['stock_id'] = stock_id  # 更新推荐对象中的股票ID
        
        print(f"\n{i}. {rec['stock_name']}({stock_id}) - {rec['action']}")
        print(f"   评分: {rec['final_score']:.2f}  推荐仓位: {rec['position']}")
        print(f"   板块数: {rec['board_count']}  概念数: {rec['concept_count']}")
        
        if rec['top_boards']:
            print(f"   主要板块: {', '.join(rec['top_boards'])}")
        if rec['top_concepts']:
            print(f"   主要概念: {', '.join(rec['top_concepts'])}")
            
        print("   推荐理由:")
        for reason in rec['reasons']:
            print(f"   - {reason}")
    
    print("\n" + "=" * 80)
    
    # 导出到CSV
    output_file = '../../top_stock_recommendations.csv'
    
    # 准备CSV数据
    data = []
    for rec in recommendations:
        # 确保股票ID为6位数字字符串，保留前导零
        stock_id = str(rec['stock_id']).zfill(6)
        
        reasons = '; '.join(rec['reasons'])
        top_boards = '; '.join(rec['top_boards']) if rec['top_boards'] else ''
        top_concepts = '; '.join(rec['top_concepts']) if rec['top_concepts'] else ''
        
        data.append({
            'stock_id': stock_id,  # 使用格式化后的6位股票ID
            'stock_name': rec['stock_name'],
            'score': rec['final_score'],
            'action': rec['action'],
            'position': rec['position'],
            'board_count': rec['board_count'],
            'concept_count': rec['concept_count'],
            'reasons': reasons,
            'top_boards': top_boards,
            'top_concepts': top_concepts,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 创建DataFrame并导出到CSV
    df = pd.DataFrame(data)
    df.to_csv(output_file, index=False)
    
    yfylog.logger.info(f"推荐结果已导出到 {output_file}")
    print(f"推荐结果已保存到 {output_file}")
    
    # 生成一段msg
    msg = "\n股票相似度分析 - {}\n".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    stock_list = []
    for rec in recommendations:
        # 确保股票ID为6位数字字符串，保留前导零
        stock_id = str(rec['stock_id']).zfill(6)
        stock_list.append(stock_id)
        # 获取主要概念和板块
        top_concepts = ', '.join(rec['top_concepts'][:3]) if rec['top_concepts'] else '无'
        top_boards = ', '.join(rec['top_boards']) if rec['top_boards'] else '无'
        
        msg += f"股票: #{stock_id} {rec['stock_name']}, 评分: {rec['final_score']:.2f}, 板块: {top_boards}, 概念: {top_concepts}\n"
    
    # 将股票列表保存到Redis的STRATEGY_SIMILARITY键中
    try:
        redis_mgr = StockRedisMgr()
        # 获取当前日期作为键的一部分
        current_date = datetime.now().strftime('%Y%m%d')
        # 使用STRATEGY_SIMILARITY作为键前缀，按日期保存
        key = redis_mgr.build_key(RedisKeyPrefix.STRATEGY_SIMILARITY.value, current_date)
        # 保存股票列表到Redis，设置过期时间为90天（7776000秒）
        success = redis_mgr.set_value(key, stock_list, expire=7776000)
        if success:
            yfylog.logger.info(f"成功将{len(stock_list)}只相似度推荐股票保存到Redis，键名: {key}")
    except Exception as e:
        yfylog.logger.error(f"保存相似度推荐股票到Redis失败: {str(e)}")
    
    yfylog.logger.info(f"-- 相似度推荐股票: {msg}\n")
    #print(f"\n{msg}")
    yfydm.push_wx_msg(msgStr=f"{msg}\n", type=WxMsgType.STOCK_ANALYSIS_ADVICE)


if __name__ == "__main__":
    run()
