#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据处理模块
对原始数据进行清洗、转换和预处理
"""

import pandas as pd
import numpy as np
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理类，对原始数据进行清洗和预处理"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = logger
        
    def process_similarity_data(self, df):
        """
        处理相似性数据
        
        Args:
            df: 原始相似性数据DataFrame
            
        Returns:
            pandas.DataFrame: 处理后的数据
        """
        self.logger.info("开始处理相似性数据")
        
        # 检查必要列是否存在
        required_columns = ['stock_id', 'stock_name', 'similarity']
        if not all(col in df.columns for col in required_columns):
            missing = [col for col in required_columns if col not in df.columns]
            self.logger.error(f"缺少必要列: {', '.join(missing)}")
            raise ValueError(f"数据缺少必要列: {', '.join(missing)}")
        
        # 复制数据，避免修改原始数据
        processed_df = df.copy()
        
        # 数据清洗：处理缺失值
        self.logger.info(f"原始数据形状: {processed_df.shape}")
        processed_df = processed_df.dropna(subset=['stock_id', 'similarity'])
        self.logger.info(f"清洗后数据形状: {processed_df.shape}")
        
        # 数据转换：确保股票代码为字符串
        processed_df['stock_id'] = processed_df['stock_id'].astype(str)
        
        # 数据转换：确保相似度为浮点数
        processed_df['similarity'] = pd.to_numeric(processed_df['similarity'], errors='coerce')
        processed_df = processed_df.dropna(subset=['similarity'])
        
        # 删除重复项
        processed_df = processed_df.drop_duplicates(subset=['stock_id'])
        
        # 排序
        processed_df = processed_df.sort_values(by='similarity', ascending=False)
        
        self.logger.info("相似性数据处理完成")
        return processed_df
        
    def calculate_statistics(self, df):
        """
        计算统计指标
        
        Args:
            df: 处理后的相似性数据
            
        Returns:
            dict: 统计指标
        """
        stats = {
            'count': len(df),
            'mean_similarity': df['similarity'].mean(),
            'median_similarity': df['similarity'].median(),
            'min_similarity': df['similarity'].min(),
            'max_similarity': df['similarity'].max(),
            'std_similarity': df['similarity'].std()
        }
        
        self.logger.info(f"计算统计指标: {stats}")
        return stats
        
    def merge_board_concept_data(self, board_df, concept_df):
        """
        合并板块和概念数据
        
        Args:
            board_df: 板块相似性数据
            concept_df: 概念相似性数据
            
        Returns:
            pandas.DataFrame: 合并后的数据
        """
        self.logger.info("合并板块和概念数据")
        
        # 合并前的记录数
        board_count = len(board_df)
        concept_count = len(concept_df)
        
        # 以股票ID为键合并数据
        merged = pd.merge(
            board_df, 
            concept_df, 
            on='stock_id', 
            how='outer',
            suffixes=('_board', '_concept')
        )
        
        # 合并后的记录数
        merged_count = len(merged)
        
        self.logger.info(f"合并前: 板块数据 {board_count} 条，概念数据 {concept_count} 条")
        self.logger.info(f"合并后: {merged_count} 条记录")
        
        return merged 