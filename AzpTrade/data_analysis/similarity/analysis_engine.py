#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析引擎模块
负责对处理后的数据进行分析，挖掘潜在交易机会
"""

import pandas as pd
import numpy as np
from collections import defaultdict
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AnalysisEngine:
    """分析引擎类，负责数据分析和挖掘"""
    
    def __init__(self, similarity_threshold=85.0, top_n=10):
        """
        初始化分析引擎
        
        Args:
            similarity_threshold: 相似性阈值，只分析相似性高于此值的股票
            top_n: 每个板块/概念选取的前N只股票
        """
        self.logger = logger
        self.similarity_threshold = similarity_threshold
        self.top_n = top_n
        
    def analyze_board_similarity(self, board_df):
        """
        分析板块相似性数据
        
        Args:
            board_df: 处理后的板块相似性数据
            
        Returns:
            dict: 板块分析结果
        """
        self.logger.info(f"开始分析板块相似性数据，阈值: {self.similarity_threshold}")
        
        # 筛选高相似性股票
        high_similarity = board_df[board_df['similarity'] >= self.similarity_threshold]
        
        # 按板块分组并选取每个板块中相似性最高的N只股票
        board_stocks = defaultdict(list)
        
        for board_id in high_similarity['board_id'].unique():
            board_group = high_similarity[high_similarity['board_id'] == board_id]
            board_name = board_group['board_name'].iloc[0]
            
            # 获取该板块相似性最高的N只股票
            top_stocks = board_group.head(self.top_n)
            
            board_stocks[board_id] = {
                'board_name': board_name,
                'top_stocks': top_stocks.to_dict('records'),
                'total_stocks': board_group['total_stocks'].iloc[0] if 'total_stocks' in board_group.columns else len(board_group),
                'avg_similarity': board_group['similarity'].mean()
            }
            
        self.logger.info(f"板块分析完成，共 {len(board_stocks)} 个板块符合条件")
        return board_stocks
        
    def analyze_concept_similarity(self, concept_df):
        """
        分析概念相似性数据
        
        Args:
            concept_df: 处理后的概念相似性数据
            
        Returns:
            dict: 概念分析结果
        """
        self.logger.info(f"开始分析概念相似性数据，阈值: {self.similarity_threshold}")
        
        # 筛选高相似性股票
        high_similarity = concept_df[concept_df['similarity'] >= self.similarity_threshold]
        
        # 按概念分组并选取每个概念中相似性最高的N只股票
        concept_stocks = defaultdict(list)
        
        for concept_id in high_similarity['concept_id'].unique():
            concept_group = high_similarity[high_similarity['concept_id'] == concept_id]
            concept_name = concept_group['concept_name'].iloc[0]
            
            # 获取该概念相似性最高的N只股票
            top_stocks = concept_group.head(self.top_n)
            
            concept_stocks[concept_id] = {
                'concept_name': concept_name,
                'top_stocks': top_stocks.to_dict('records'),
                'total_stocks': concept_group['total_stocks'].iloc[0] if 'total_stocks' in concept_group.columns else len(concept_group),
                'avg_similarity': concept_group['similarity'].mean()
            }
            
        self.logger.info(f"概念分析完成，共 {len(concept_stocks)} 个概念符合条件")
        return concept_stocks
        
    def integrate_analysis(self, board_analysis, concept_analysis):
        """
        整合板块和概念分析结果
        
        Args:
            board_analysis: 板块分析结果
            concept_analysis: 概念分析结果
            
        Returns:
            dict: 整合后的分析结果
        """
        self.logger.info("开始整合板块和概念分析结果")
        
        # 用于存储股票的综合评分
        stock_scores = defaultdict(lambda: {'score': 0, 'board_count': 0, 'concept_count': 0, 'details': []})
        
        # 处理板块分析结果
        for board_id, board_info in board_analysis.items():
            for stock in board_info['top_stocks']:
                stock_id = stock['stock_id']
                stock_scores[stock_id]['score'] += stock['similarity'] * 0.6  # 板块权重0.6
                stock_scores[stock_id]['board_count'] += 1
                stock_scores[stock_id]['stock_name'] = stock['stock_name']
                stock_scores[stock_id]['details'].append({
                    'type': 'board',
                    'id': board_id,
                    'name': board_info['board_name'],
                    'similarity': stock['similarity']
                })
        
        # 处理概念分析结果
        for concept_id, concept_info in concept_analysis.items():
            for stock in concept_info['top_stocks']:
                stock_id = stock['stock_id']
                stock_scores[stock_id]['score'] += stock['similarity'] * 0.4  # 概念权重0.4
                stock_scores[stock_id]['concept_count'] += 1
                stock_scores[stock_id]['stock_name'] = stock['stock_name']
                stock_scores[stock_id]['details'].append({
                    'type': 'concept',
                    'id': concept_id,
                    'name': concept_info['concept_name'],
                    'similarity': stock['similarity']
                })
        
        # 计算最终评分
        for stock_id, info in stock_scores.items():
            # 标准化评分
            info['normalized_score'] = info['score'] / (info['board_count'] * 0.6 + info['concept_count'] * 0.4) if (info['board_count'] + info['concept_count']) > 0 else 0
            
            # 计算交叉因子（同时出现在多个板块和概念中的股票得分更高）
            info['cross_factor'] = min(3, info['board_count'] + info['concept_count']) / 3
            
            # 最终评分
            info['final_score'] = info['normalized_score'] * (1 + 0.5 * info['cross_factor'])
        
        # 按最终评分排序
        sorted_stocks = sorted(
            stock_scores.items(), 
            key=lambda x: x[1]['final_score'], 
            reverse=True
        )
        
        # 构建返回结果
        result = {
            'top_stocks': [{
                'stock_id': stock_id,
                'stock_name': info['stock_name'],
                'final_score': info['final_score'],
                'normalized_score': info['normalized_score'],
                'board_count': info['board_count'],
                'concept_count': info['concept_count'],
                'details': info['details']
            } for stock_id, info in sorted_stocks[:self.top_n * 2]],  # 返回评分最高的股票
            'board_analysis': board_analysis,
            'concept_analysis': concept_analysis
        }
        
        self.logger.info(f"分析整合完成，共有 {len(sorted_stocks)} 只股票进入候选池")
        return result 