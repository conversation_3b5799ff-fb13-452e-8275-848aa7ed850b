#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
股票分析交易系统 - 主程序入口
基于板块和概念相似性分析提供交易建议
"""

import os
import sys
import argparse
from data_loader import DataLoader
from data_processor import DataProcessor
from analysis_engine import AnalysisEngine
from trading_strategy import TradingStrategy
from visualization import Visualizer

def parse_args():
    parser = argparse.ArgumentParser(description="股票分析交易系统")
    parser.add_argument("--board_file", default="top_board_similar_stocks.csv", help="板块相似性文件路径")
    parser.add_argument("--concept_file", default="top_concept_similar_stocks.csv", help="概念相似性文件路径")
    parser.add_argument("--similarity_threshold", type=float, default=85.0, help="相似性阈值")
    parser.add_argument("--top_n", type=int, default=10, help="选取前N只股票")
    parser.add_argument("--output_dir", default="output", help="输出目录")
    
    return parser.parse_args()

def main():
    args = parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 1. 加载数据
    data_loader = DataLoader()
    board_data = data_loader.load_csv(args.board_file)
    concept_data = data_loader.load_csv(args.concept_file)
    
    # 2. 数据处理
    data_processor = DataProcessor()
    processed_board = data_processor.process_similarity_data(board_data)
    processed_concept = data_processor.process_similarity_data(concept_data)
    
    # 3. 分析引擎
    analysis_engine = AnalysisEngine(
        similarity_threshold=args.similarity_threshold,
        top_n=args.top_n
    )
    board_analysis = analysis_engine.analyze_board_similarity(processed_board)
    concept_analysis = analysis_engine.analyze_concept_similarity(processed_concept)
    integrated_analysis = analysis_engine.integrate_analysis(board_analysis, concept_analysis)
    
    # 4. 交易策略
    trading_strategy = TradingStrategy()
    recommendations = trading_strategy.generate_recommendations(integrated_analysis)
    portfolio = trading_strategy.build_portfolio(recommendations)
    
    # 5. 可视化结果
    visualizer = Visualizer(output_dir=args.output_dir)
    visualizer.plot_top_stocks(integrated_analysis)
    visualizer.plot_recommendations(recommendations)
    visualizer.generate_report(portfolio)
    
    print(f"分析完成，结果已保存至 {args.output_dir} 目录")

if __name__ == "__main__":
    main() 