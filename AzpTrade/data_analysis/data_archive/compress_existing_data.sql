-- u624bu52a8u538bu7f29u73b0u6709u6570u636e

-- 1. u5df2u7ecfu6210u529fu542fu7528u4e86u538bu7f29u529fu80fduff0cu73b0u5728u53eau9700u8981u624bu52a8u538bu7f29u5b58u91cfu6570u636e

-- u67e5u770bu53efu7528u7684u5206u533a
SELECT show_chunks('stock_prices');

-- u624bu52a8u538bu7f29u5206u533a
-- u5982u679cu60a8u770bu5230u6709u5206u533auff0cu53efu4ee5u8fd0u884cu4ee5u4e0bu547du4ee4u538bu7f29
-- u6ce8u610fu66ffu6362u5206u533au540du79f0
-- u4f8bu5982uff1a SELECT compress_chunk('_timescaledb_internal._hyper_1_1_chunk');

-- u67e5u770bu538bu7f29u8bbeu7f6e
SELECT * FROM timescaledb_information.compression_settings;

-- u67e5u770bu5f53u524du7684u538bu7f29u7b56u7565
SELECT * FROM timescaledb_information.policies WHERE job_type = 'compress_chunks';

-- u9ad8u7ea7u5e94u7528 - u624bu52a8u538bu7f29u6240u6709u65e7u5206u533a
-- u8fd9u4e2au547du4ee4u4f1au5c1du8bd5u538bu7f29u6240u6709u65e7u7684u5206u533a
DO $$
DECLARE
    chunk_rec RECORD;
BEGIN
    FOR chunk_rec IN SELECT chunk_name FROM show_chunks('stock_prices') LOOP
        BEGIN
            EXECUTE format('SELECT compress_chunk(%L)', chunk_rec.chunk_name);
            RAISE NOTICE 'Successfully compressed chunk: %', chunk_rec.chunk_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Failed to compress chunk: %, Error: %', chunk_rec.chunk_name, SQLERRM;
        END;
    END LOOP;
END $$;

-- u67e5u770bu538bu7f29u540eu6240u6709u8868u7684u5927u5c0fu548cu538bu7f29u7387
SELECT
    table_schema,
    table_name,
    pg_size_pretty(pg_total_relation_size(quote_ident(table_schema) || '.' || quote_ident(table_name))) as total_size
FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY pg_total_relation_size(quote_ident(table_schema) || '.' || quote_ident(table_name)) DESC
LIMIT 10;
