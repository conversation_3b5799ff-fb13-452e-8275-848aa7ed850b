-- 启用TimescaleDB压缩功能

-- 1. 启用压缩并设置压缩策略
ALTER TABLE stock_prices SET (
  timescaledb.compress,
  timescaledb.compress_segmentby = 'symbol',
  timescaledb.compress_orderby = 'time DESC'
);

-- 2. 设置自动压缩策略 - 一天前的数据自动压缩
SELECT add_compression_policy('stock_prices', INTERVAL '1 day');

-- 3. 查看压缩设置
SELECT * FROM timescaledb_information.compression_settings;

-- 4. 手动压缩现有数据 (可选)
-- 这会压缩所有1天前的数据分区
SELECT compress_chunk(chunk)
FROM show_chunks('stock_prices') AS chunk
WHERE chunk_start < now() - INTERVAL '1 day';

-- 5. 查看压缩状态
SELECT 
    hypertable_name,
    compression_enabled,
    before_compression_total_bytes,
    after_compression_total_bytes,
    (before_compression_total_bytes - after_compression_total_bytes) / before_compression_total_bytes::float * 100 as compression_ratio
FROM timescaledb_information.compressed_hypertable_stats;
