#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到Python路径 - 确保能找到trade模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = "/root/azptrade/AzpTrade"  # 直接指定完整路径
print(f"添加路径到sys.path: {project_root}")
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import threading
import time
import pandas as pd
import psycopg2
import gzip
import base64
import json
import datetime
import traceback

import trade.log.YfyLog as yfyLog
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix
from trade.redis.StockRedisSubscriberMgr import RedisSubscriber

'''
[yfydata_redisData.py:72] 获取股票字段信息: [{'fieldType': '1', 'fieldName': 'symbol', 'remark': '股票代码'}, {'fieldType': '2', 'fieldName': 'name', 'remark': '股票名称'}, {'fieldType': '3', 'fieldName': 'open', 'remark': '开盘价'}, {'fieldType': '4', 'fieldName': 'close', 'remark': '收盘价'}, {'fieldType': '5', 'fieldName': 'high', 'remark': '最高价'}, {'fieldType': '6', 'fieldName': 'low', 'remark': '最低价'}, {'fieldType': '7', 'fieldName': 'turnOverRate', 'remark': '换手率'}, {'fieldType': '8', 'fieldName': 'openChangePercent', 'remark': '开盘价涨跌幅'}, {'fieldType': '9', 'fieldName': 'changeRate', 'remark': '涨速'}, {'fieldType': '10', 'fieldName': 'changePercent', 'remark': '涨跌幅'}, {'fieldType': '11', 'fieldName': 'changePercent5M', 'remark': '5分钟涨跌幅'}, {'fieldType': '12', 'fieldName': 'changePercent3D', 'remark': '3日涨跌幅'}, {'fieldType': '13', 'fieldName': 'changePercent5D', 'remark': '5日涨跌幅'}, {'fieldType': '14', 'fieldName': 'changePercent10D', 'remark': '10日涨跌幅'}, {'fieldType': '15', 'fieldName': 'changePercent20D', 'remark': '20日涨跌幅'}, {'fieldType': '16', 'fieldName': 'changePercent30D', 'remark': '30日涨跌幅'}, {'fieldType': '17', 'fieldName': 'changePercent60D', 'remark': '60日涨跌幅'}, {'fieldType': '18', 'fieldName': 'volume', 'remark': '成交量'}, {'fieldType': '19', 'fieldName': 'volumeAmount', 'remark': '成交额'}, {'fieldType': '20', 'fieldName': 'volumeMin', 'remark': '分钟量'}, {'fieldType': '21', 'fieldName': 'boardRank', 'remark': '板块排名'}, {'fieldType': '22', 'fieldName': 'boardMainInFlowAmount', 'remark': '板块主力净流入额'}, {'fieldType': '23', 'fieldName': 'boardInFlowRank', 'remark': '板块主净排名'}, {'fieldType': '24', 'fieldName': 'boardOpen', 'remark': '板块开盘价'}, {'fieldType': '25', 'fieldName': 'boardClose', 'remark': '板块收盘价'}, {'fieldType': '26', 'fieldName': 'boardHigh', 'remark': '板块最高价'}, {'fieldType': '27', 'fieldName': 'boardLow', 'remark': '板块最低价'}, {'fieldType': '28', 'fieldName': 'boardChangePercent', 'remark': '板块涨跌幅'}, {'fieldType': '29', 'fieldName': 'boardChangePercentRank', 'remark': '板块涨跌幅排名'}, {'fieldType': '30', 'fieldName': 'boardVolumeAmountRank', 'remark': '板块成交额排名'}, {'fieldType': '31', 'fieldName': 'boardMainInFlowAmountRank', 'remark': '板块主净排名'}, {'fieldType': '32', 'fieldName': 'totalLimitUpCount', 'remark': '5日涨停数'}, {'fieldType': '33', 'fieldName': 'totalLimitDownCount', 'remark': '5日跌停数'}, {'fieldType': '34', 'fieldName': 'isLimitUp', 'remark': '是否涨停'}, {'fieldType': '35', 'fieldName': 'isLimitDown', 'remark': '是否跌停'}, {'fieldType': '36', 'fieldName': 'ma5', 'remark': 'MA5'}, {'fieldType': '37', 'fieldName': 'ma10', 'remark': 'MA10'}, {'fieldType': '38', 'fieldName': 'ma20', 'remark': 'MA20'}, {'fieldType': '39', 'fieldName': 'ma60', 'remark': 'MA60'}, {'fieldType': '40', 'fieldName': 'ma250', 'remark': 'MA250'}, {'fieldType': '41', 'fieldName': 'ema21', 'remark': 'EMA21'}, {'fieldType': '42', 'fieldName': 'ema55', 'remark': 'EMA55'}, {'fieldType': '43', 'fieldName': 'ema89', 'remark': 'EMA89'}, {'fieldType': '44', 'fieldName': 'ema144', 'remark': 'EMA144'}, {'fieldType': '45', 'fieldName': 'rank', 'remark': '个股排名'}, {'fieldType': '46', 'fieldName': 'mainInFlowAmount', 'remark': '个股主净'}, {'fieldType': '47', 'fieldName': 'mainInFlowRank', 'remark': '个股主净排名'}, {'fieldType': '48', 'fieldName': 'xlMainInFlowAmount', 'remark': '新浪主净'}, {'fieldType': '49', 'fieldName': 'xl_mainInFlowRank', 'remark': '新浪主净排名'}, {'fieldType': '50', 'fieldName': 'kplMainInFlowAmount', 'remark': '开盘啦主净'}, {'fieldType': '51', 'fieldName': 'kpl_mainInFlowRank', 'remark': '开盘啦主净排名'}, {'fieldType': '52', 'fieldName': 'lianbanCount', 'remark': '连板数'}, {'fieldType': '53', 'fieldName': 'conceptName', 'remark': '概念名称'}, {'fieldType': '54', 'fieldName': 'conceptOpen', 'remark': '概念开盘价'}, {'fieldType': '55', 'fieldName': 'conceptClose', 'remark': '概念收盘价'}, {'fieldType': '56', 'fieldName': 'conceptHigh', 'remark': '概念最高价'}, {'fieldType': '57', 'fieldName': 'conceptLow', 'remark': '概念最低价'}, {'fieldType': '58', 'fieldName': 'conceptChangePercent', 'remark': '概念涨跌幅'}, {'fieldType': '59', 'fieldName': 'conceptRank', 'remark': '概念排名'}, {'fieldType': '60', 'fieldName': 'conceptMainInFlowAmount', 'remark': '概念主净'}, {'fieldType': '61', 'fieldName': 'conceptInFlowRank', 'remark': '概念主净排名'}, {'fieldType': '62', 'fieldName': 'conceptSpecialName', 'remark': '概异名称'}, {'fieldType': '63', 'fieldName': 'conceptChangePercentRank', 'remark': '概念个股涨跌幅排名'}, {'fieldType': '64', 'fieldName': 'conceptVolumeAmountRank', 'remark': '概念个股成交额排名'}, {'fieldType': '65', 'fieldName': 'conceptMainInFlowAmountRank', 'remark': '概念个股主净排名'}, {'fieldType': '66', 'fieldName': 'buy_signal_list', 'remark': '买信号列表'}, {'fieldType': '67', 'fieldName': 'sell_signal_list', 'remark': '卖信号列表'}, {'fieldType': '68', 'fieldName': 'txMainInFlowAmount', 'remark': '腾讯主净'}, {'fieldType': '69', 'fieldName': 'txMainInFlowRank', 'remark': '腾讯主净排名'}, {'fieldType': '70', 'fieldName': 'ma3', 'remark': 'MA3'}]
'''

# Redis推送频道常量
PUSH_STOCK_INFO = "PUSH_STOCK_INFO"  # 推送股票信息

class StockDataArchiver(threading.Thread):
    """股票数据归档线程，专门负责将股票数据存储到TimescaleDB"""
    
    def __init__(self, interval=10, field_info=None):
        """初始化归档线程
        Args:
            interval: 数据存储间隔（秒）
        """
        super().__init__()
        self.daemon = True  # 设为守护线程，主线程结束时自动结束
        self.interval = interval
        self.running = False
        self.last_data = None
        self.last_store_time = 0
        self.field_info = field_info
        self.conn_params = {
            "dbname": "stock_data",
            "user": "postgres",
            "password": "MQH1ylhedpia7oM5",  # 数据库密码
            "host": "localhost",
            "port": "5432"
        }
        
        # 初始化订阅者
        self.subscriber = None
        
    def setup_database(self):
        """初始化TimescaleDB数据库和表结构"""
        try:
            # 连接到默认数据库创建我们的数据库
            conn = psycopg2.connect(
                dbname="postgres", 
                user=self.conn_params["user"],
                password=self.conn_params["password"],
                host=self.conn_params["host"]
            )
            conn.autocommit = True
            cur = conn.cursor()
            
            # 检查数据库是否存在，不存在则创建
            cur.execute(f"SELECT 1 FROM pg_database WHERE datname = '{self.conn_params['dbname']}'")
            if cur.fetchone() is None:
                cur.execute(f"CREATE DATABASE {self.conn_params['dbname']}")
            
            cur.close()
            conn.close()
            
            # 连接到我们的数据库并设置TimescaleDB
            conn = psycopg2.connect(**self.conn_params)
            conn.autocommit = True
            cur = conn.cursor()
            
            # 创建TimescaleDB扩展
            cur.execute("CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;")
            
            # 创建股票价格表 - 保持原始字段名
            cur.execute("""
            CREATE TABLE IF NOT EXISTS stock_prices (
                time TIMESTAMPTZ NOT NULL,
                symbol TEXT NOT NULL,
                name TEXT,
                open NUMERIC(10,2),
                close NUMERIC(10,2),
                high NUMERIC(10,2),
                low NUMERIC(10,2),
                "turnOverRate" NUMERIC(8,2),
                "openChangePercent" NUMERIC(8,2),
                "changeRate" NUMERIC(8,2),
                "changePercent" NUMERIC(8,2),
                "changePercent5M" NUMERIC(8,2),
                "changePercent3D" NUMERIC(8,2),
                "changePercent5D" NUMERIC(8,2),
                "changePercent10D" NUMERIC(8,2),
                "changePercent20D" NUMERIC(8,2),
                "changePercent30D" NUMERIC(8,2),
                "changePercent60D" NUMERIC(8,2),
                volume BIGINT,
                "volumeAmount" NUMERIC(20,2),
                "volumeMin" NUMERIC(20,2),
                
                -- 板块数据
                "boardRank" INTEGER,
                "boardMainInFlowAmount" NUMERIC(16,2),
                "boardInFlowRank" INTEGER,
                "boardOpen" NUMERIC(10,2),
                "boardClose" NUMERIC(10,2),
                "boardHigh" NUMERIC(10,2),
                "boardLow" NUMERIC(10,2),
                "boardChangePercent" NUMERIC(8,2),
                "boardChangePercentRank" INTEGER,
                "boardVolumeAmountRank" INTEGER,
                "boardMainInFlowAmountRank" INTEGER,
                
                -- 涨跌停相关
                "totalLimitUpCount" INTEGER,
                "totalLimitDownCount" INTEGER,
                "isLimitUp" BOOLEAN,
                "isLimitDown" BOOLEAN,
                "lianbanCount" INTEGER,
                
                -- 均线数据
                "ma3" NUMERIC(10,2),
                "ma5" NUMERIC(10,2),
                "ma10" NUMERIC(10,2),
                "ma20" NUMERIC(10,2),
                "ma60" NUMERIC(10,2),
                "ma250" NUMERIC(10,2),
                "ema21" NUMERIC(10,2),
                "ema55" NUMERIC(10,2),
                "ema89" NUMERIC(10,2),
                "ema144" NUMERIC(10,2),
                
                -- 排名与资金流
                "rank" INTEGER,
                "mainInFlowAmount" NUMERIC(16,2),
                "mainInFlowRank" INTEGER,
                "xlMainInFlowAmount" NUMERIC(16,2),
                "xl_mainInFlowRank" INTEGER,
                "kplMainInFlowAmount" NUMERIC(16,2),
                "kpl_mainInFlowRank" INTEGER,
                "txMainInFlowAmount" NUMERIC(16,2),
                "txMainInFlowRank" INTEGER,
                
                -- 概念数据
                "conceptName" TEXT,
                "conceptOpen" NUMERIC(10,2),
                "conceptClose" NUMERIC(10,2),
                "conceptHigh" NUMERIC(10,2),
                "conceptLow" NUMERIC(10,2),
                "conceptChangePercent" NUMERIC(8,2),
                "conceptRank" INTEGER,
                "conceptMainInFlowAmount" NUMERIC(16,2),
                "conceptInFlowRank" INTEGER,
                "conceptSpecialName" TEXT,
                "conceptChangePercentRank" INTEGER,
                "conceptVolumeAmountRank" INTEGER,
                "conceptMainInFlowAmountRank" INTEGER,
                
                -- 信号数据
                "buy_signal_list" JSONB,
                "sell_signal_list" JSONB
            );
            """) 
            
            # 创建超表（TimescaleDB核心功能）
            cur.execute("SELECT create_hypertable('stock_prices', 'time', if_not_exists => TRUE);")
            
            # 创建索引以加速查询
            cur.execute("CREATE INDEX IF NOT EXISTS idx_stock_prices_symbol ON stock_prices (symbol);")
            cur.execute("CREATE INDEX IF NOT EXISTS idx_stock_prices_time ON stock_prices (time);")
            cur.execute("CREATE INDEX IF NOT EXISTS idx_stock_prices_concept ON stock_prices (\"conceptName\");")
            
            # 设置数据保留策略（保留7天数据）
            cur.execute("SELECT add_retention_policy('stock_prices', INTERVAL '7 days', if_not_exists => TRUE);")
            
            cur.close()
            conn.close()
            
            yfyLog.logger.info("TimescaleDB数据库和表结构初始化成功")
            return True
        except Exception as e:
            yfyLog.logger.error(f"初始化TimescaleDB失败: {e}")
            yfyLog.logger.error(traceback.format_exc())
            return False
    
    def store_data(self, data):
        """将最新的股票数据存储到TimescaleDB
        Args:
            data: 原始压缩的股票数据（base64编码的gzip压缩数据）
        """
        try:
            # 解码和解压数据
            compressed_bytes = base64.b64decode(data)
            decompressed_bytes = gzip.decompress(compressed_bytes)
            text = decompressed_bytes.decode('utf-8')
            j_text = json.loads(text)
            
            # 解析数据
            df = pd.DataFrame(j_text)
            if self.field_info:
                df.rename(columns=self.field_info, inplace=True)
            
            # 格式化数据
            now = datetime.datetime.now()
            
            # 连接数据库
            conn = psycopg2.connect(**self.conn_params)
            cur = conn.cursor()
            
            # 创建批量插入语句 - 和表结构对应的所有字段
            values = []
            for _, row in df.iterrows():
                try:
                    # 解析JSON字段
                    buy_signals = json.dumps(row.get('buy_signal_list', [])) if isinstance(row.get('buy_signal_list'), list) else '{}'
                    sell_signals = json.dumps(row.get('sell_signal_list', [])) if isinstance(row.get('sell_signal_list'), list) else '{}'
                    
                    values.append((
                        now,  # time
                        str(row.get('symbol', '')),  # symbol
                        str(row.get('name', '')),  # name
                        float(row.get('open', 0)),  # open
                        float(row.get('close', 0)),  # close
                        float(row.get('high', 0)),  # high
                        float(row.get('low', 0)),  # low
                        float(row.get('turnOverRate', 0)),  # turnover_rate
                        float(row.get('openChangePercent', 0)),  # open_change_percent
                        float(row.get('changeRate', 0)),  # change_rate
                        float(row.get('changePercent', 0)),  # change_percent
                        float(row.get('changePercent5M', 0)),  # change_percent_5m
                        float(row.get('changePercent3D', 0)),  # change_percent_3d
                        float(row.get('changePercent5D', 0)),  # change_percent_5d
                        float(row.get('changePercent10D', 0)),  # change_percent_10d
                        float(row.get('changePercent20D', 0)),  # change_percent_20d
                        float(row.get('changePercent30D', 0)),  # change_percent_30d
                        float(row.get('changePercent60D', 0)),  # change_percent_60d
                        int(row.get('volume', 0)),  # volume
                        float(row.get('volumeAmount', 0)),  # volume_amount
                        float(row.get('volumeMin', 0)),  # volume_min
                        
                        # 板块数据
                        int(row.get('boardRank', 0)),  # board_rank
                        float(row.get('boardMainInFlowAmount', 0)),  # board_main_inflow_amount
                        int(row.get('boardInFlowRank', 0)),  # board_inflow_rank
                        float(row.get('boardOpen', 0)),  # board_open
                        float(row.get('boardClose', 0)),  # board_close
                        float(row.get('boardHigh', 0)),  # board_high
                        float(row.get('boardLow', 0)),  # board_low
                        float(row.get('boardChangePercent', 0)),  # board_change_percent
                        int(row.get('boardChangePercentRank', 0)),  # board_change_percent_rank
                        int(row.get('boardVolumeAmountRank', 0)),  # board_volume_amount_rank
                        int(row.get('boardMainInFlowAmountRank', 0)),  # board_main_inflow_amount_rank
                        
                        # 涨跌停相关
                        int(row.get('totalLimitUpCount', 0)),  # total_limit_up_count
                        int(row.get('totalLimitDownCount', 0)),  # total_limit_down_count
                        bool(row.get('isLimitUp', False)),  # is_limit_up
                        bool(row.get('isLimitDown', False)),  # is_limit_down
                        int(row.get('lianbanCount', 0)),  # lianban_count
                        
                        # 均线数据
                        float(row.get('ma3', 0)),  # ma3
                        float(row.get('ma5', 0)),  # ma5
                        float(row.get('ma10', 0)),  # ma10
                        float(row.get('ma20', 0)),  # ma20
                        float(row.get('ma60', 0)),  # ma60
                        float(row.get('ma250', 0)),  # ma250
                        float(row.get('ema21', 0)),  # ema21
                        float(row.get('ema55', 0)),  # ema55
                        float(row.get('ema89', 0)),  # ema89
                        float(row.get('ema144', 0)),  # ema144
                        
                        # 排名与资金流
                        int(row.get('rank', 0)),  # rank
                        float(row.get('mainInFlowAmount', 0)),  # main_inflow_amount
                        int(row.get('mainInFlowRank', 0)),  # main_inflow_rank
                        float(row.get('xlMainInFlowAmount', 0)),  # xl_main_inflow_amount
                        int(row.get('xl_mainInFlowRank', 0)),  # xl_main_inflow_rank
                        float(row.get('kplMainInFlowAmount', 0)),  # kpl_main_inflow_amount
                        int(row.get('kpl_mainInFlowRank', 0)),  # kpl_main_inflow_rank
                        float(row.get('txMainInFlowAmount', 0)),  # tx_main_inflow_amount
                        int(row.get('txMainInFlowRank', 0)),  # tx_main_inflow_rank
                        
                        # 概念数据
                        str(row.get('conceptName', '')),  # concept_name
                        float(row.get('conceptOpen', 0)),  # concept_open
                        float(row.get('conceptClose', 0)),  # concept_close
                        float(row.get('conceptHigh', 0)),  # concept_high
                        float(row.get('conceptLow', 0)),  # concept_low
                        float(row.get('conceptChangePercent', 0)),  # concept_change_percent
                        int(row.get('conceptRank', 0)),  # concept_rank
                        float(row.get('conceptMainInFlowAmount', 0)),  # concept_main_inflow_amount
                        int(row.get('conceptInFlowRank', 0)),  # concept_inflow_rank
                        str(row.get('conceptSpecialName', '')),  # concept_special_name
                        int(row.get('conceptChangePercentRank', 0)),  # concept_change_percent_rank
                        int(row.get('conceptVolumeAmountRank', 0)),  # concept_volume_amount_rank
                        int(row.get('conceptMainInFlowAmountRank', 0)),  # concept_main_inflow_amount_rank
                        
                        # 信号数据
                        buy_signals,  # buy_signal_list
                        sell_signals   # sell_signal_list
                    ))
                except (ValueError, TypeError) as e:
                    symbol = row.get('symbol', '未知')
                    yfyLog.logger.warning(f"处理股票 {symbol} 数据时出错: {e}")
                    continue
            
            # 如果有有效的值，执行批量插入
            if values:
                # 创建全字段占位符
                placeholders = "(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
                
                # 生成批量插入语句
                args_str = ','.join(cur.mogrify(placeholders, x).decode('utf-8') for x in values)
                
                # 执行插入 - 保持原始字段名
                insert_sql = f"""
                INSERT INTO stock_prices (
                    time, symbol, name, open, close, high, low, "turnOverRate", "openChangePercent",
                    "changeRate", "changePercent", "changePercent5M", "changePercent3D", "changePercent5D",
                    "changePercent10D", "changePercent20D", "changePercent30D", "changePercent60D",
                    volume, "volumeAmount", "volumeMin",
                    "boardRank", "boardMainInFlowAmount", "boardInFlowRank", "boardOpen", "boardClose",
                    "boardHigh", "boardLow", "boardChangePercent", "boardChangePercentRank",
                    "boardVolumeAmountRank", "boardMainInFlowAmountRank",
                    "totalLimitUpCount", "totalLimitDownCount", "isLimitUp", "isLimitDown", "lianbanCount",
                    "ma3", "ma5", "ma10", "ma20", "ma60", "ma250", "ema21", "ema55", "ema89", "ema144",
                    "rank", "mainInFlowAmount", "mainInFlowRank", "xlMainInFlowAmount", "xl_mainInFlowRank",
                    "kplMainInFlowAmount", "kpl_mainInFlowRank", "txMainInFlowAmount", "txMainInFlowRank",
                    "conceptName", "conceptOpen", "conceptClose", "conceptHigh", "conceptLow",
                    "conceptChangePercent", "conceptRank", "conceptMainInFlowAmount", "conceptInFlowRank",
                    "conceptSpecialName", "conceptChangePercentRank", "conceptVolumeAmountRank",
                    "conceptMainInFlowAmountRank", "buy_signal_list", "sell_signal_list"
                )
                VALUES {args_str}
                """
                
                cur.execute(insert_sql)
                conn.commit()
                yfyLog.logger.info(f"成功存储 {len(values)} 条完整股票数据记录到TimescaleDB, 时间: {now}")
            
            cur.close()
            conn.close()
            
        except Exception as e:
            yfyLog.logger.error(f"存储数据到TimescaleDB失败: {e}")
            yfyLog.logger.error(traceback.format_exc())
    
    def handle_stock_data(self, channel, data):
        """处理股票数据的回调函数
        
        Args:
            channel: Redis频道名
            data: 从Redis接收到的数据
        """
        # 忽略非目标频道的消息
        if channel != PUSH_STOCK_INFO:
            return
        
        # 检查是否在交易时间内
        if not self.is_trading_time():
            return  # 非交易时间不处理数据
        
        try:
            current_time = time.time()
            
            # 检查是否达到存储间隔
            if current_time - self.last_store_time >= self.interval:
                yfyLog.logger.info(f"收到股票数据，准备存储, 频道: {channel}")
                self.store_data(data)
                self.last_store_time = current_time
        except Exception as e:
            yfyLog.logger.error(f"处理股票数据时发生异常: {e}")
            yfyLog.logger.error(traceback.format_exc())
    
    def start_subscribing(self):
        """启动Redis订阅"""
        try:
            # 创建订阅者实例
            self.subscriber = RedisSubscriber()
            
            # 注册回调函数
            self.subscriber.register_callback(PUSH_STOCK_INFO, self.handle_stock_data)
            
            # 订阅频道
            self.subscriber.subscribe([PUSH_STOCK_INFO])
            
            # 启动监听
            self.subscriber.start_listening()
            yfyLog.logger.info(f"股票数据归档订阅已启动，订阅频道: {PUSH_STOCK_INFO}")
            return True
        except Exception as e:
            yfyLog.logger.error(f"启动Redis订阅失败: {e}")
            yfyLog.logger.error(traceback.format_exc())
            return False
    
    def stop_subscribing(self):
        """停止Redis订阅"""
        if self.subscriber:
            self.subscriber.stop_listening()
            self.subscriber.unsubscribe()
            yfyLog.logger.info("股票数据归档订阅已停止")
    
    def is_trading_time(self):
        """判断当前是否为交易时间，只在交易时段采集数据
        交易时段: 9:29-10:25, 14:25-15:01
        """
        now = datetime.datetime.now()
        current_time = now.time()
        
        # 检查是否是周末 (5=星期六, 6=星期日)
        if now.weekday() >= 5:
            return False
            
        # 定义交易时间段
        morning_start = datetime.time(9, 29, 30)  # 修改上午开始时间
        morning_end = datetime.time(10, 25, 0)  # 修改上午结束时间
        afternoon_start = datetime.time(14, 25, 0)  # 修改下午开始时间
        afternoon_end = datetime.time(15, 1, 0)
        
        # 判断当前时间是否在交易时段内
        is_morning_session = morning_start <= current_time <= morning_end
        is_afternoon_session = afternoon_start <= current_time <= afternoon_end
        
        return is_morning_session or is_afternoon_session
    
    def run(self):
        """线程主函数"""
        self.running = True
        
        # 初始化数据库
        if not self.setup_database():
            yfyLog.logger.error("数据库初始化失败，归档线程退出")
            return
        
        # 启动订阅
        if not self.start_subscribing():
            yfyLog.logger.error("Redis订阅启动失败，归档线程退出")
            return
        
        yfyLog.logger.info(f"股票数据归档线程已启动，存储间隔: {self.interval}秒，仅在交易时段(9:29-10:25, 14:25-15:01)采集数据")
        
        last_log_time = time.time()  # 上次日志记录时间
        
        while self.running:
            try:
                # 检查是否为交易时间
                if not self.is_trading_time():
                    # 每隔5分钟记录一次非交易时段日志，避免日志过多
                    current_time = time.time()
                    if current_time - last_log_time >= 300:
                        yfyLog.logger.info("当前为非交易时段，暂停数据采集")
                        last_log_time = current_time
                    time.sleep(10)  # 非交易时段休眠较长时间
                    continue
                
                # 线程主循环，保持运行
                time.sleep(1)
            except Exception as e:
                yfyLog.logger.error(f"股票数据归档线程发生异常: {e}")
                yfyLog.logger.error(traceback.format_exc())
                time.sleep(5)  # 发生错误后稍微长一点的休眠
    
    def stop(self):
        """停止归档线程"""
        self.running = False
        self.stop_subscribing()
        yfyLog.logger.info("股票数据归档线程正在停止...")


def get_stock_field_info():
    """获取股票字段信息映射"""
    try:
        from trade.network import yfy_base_data
        import requests
        
        url = yfy_base_data.get_base_url() + "field/info"
        headers = {"Content-Type": "application/json"}
        payload = {}
        
        response = requests.post(url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()
        j_data = response.json()
        j_data_list = j_data.get("data", [])
        f_data = {item["fieldType"]: item["fieldName"] for item in j_data_list}
        
        yfyLog.logger.info(f"获取股票字段信息成功，共 {len(f_data)} 个字段")
        return f_data
    except Exception as e:
        yfyLog.logger.error(f"获取股票字段信息失败: {e}")
        yfyLog.logger.error(traceback.format_exc())
        
        # 返回一个基本的字段映射
        return {
            "1": "symbol", "2": "name", "3": "open", "4": "close", 
            "5": "high", "6": "low", "7": "turnOverRate", "9": "changeRate", 
            "10": "changePercent", "18": "volume", "19": "volumeAmount", 
            "34": "isLimitUp", "35": "isLimitDown", "36": "ma5", 
            "37": "ma10", "38": "ma20", "39": "ma60", "41": "ema21",
            "46": "mainInFlowAmount", "52": "lianbanCount"
        }


def start_archiver(interval=10):
    """启动股票数据归档线程
    
    Args:
        interval: 存储间隔（秒）
    
    Returns:
        StockDataArchiver: 归档线程实例
    """
    # 获取字段信息
    field_info = get_stock_field_info()
    
    # 创建并启动归档线程
    archiver = StockDataArchiver(interval=interval, field_info=field_info)
    archiver.start()
    
    return archiver


if __name__ == "__main__":
    try:
        print("启动股票数据归档服务...")
        
        # 启动归档线程
        archiver = start_archiver(interval=10)  # 每10秒存储一次
        
        # 保持程序运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n用户中断，关闭服务...")
        if 'archiver' in locals():
            archiver.stop()
            archiver.join(timeout=5)

# cd data_analysis/data_archive/
# nohup python all_stock_data_archiver.py > archiver_output_$(date +"%Y%m%d_%H%M%S").log 2>&1 &