import os
import sys
from concurrent.futures import ThreadPoolExecutor

# 获取项目根目录并添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))  # AzpTrade目录
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import threading
import time
from typing import Dict, Callable, List, Any, Optional, Union
import json
import pandas as pd
import numpy as np
import datetime
import traceback
import requests

import trade.log.YfyLog as yfyLog
from trade.redis.StockRedisMgr import StockRedisMgr, RedisKeyPrefix
from trade.redis.StockRedisSubscriberMgr import RedisSubscriber
import trade.network.YFYAccountMgr as yfyam
import trade.network.YFYDataMgr as yfydm
import gzip
import base64
import trade.manager.TimeDateMgr as dateMgr
import trade.network.yfy_base_data as yfy_b_data

# Redis推送频道常量
PUSH_STOCK_INFO = "PUSH_STOCK_INFO"  # 推送股票信息
PUSH_STOCK_POSITION_INFO = "PUSH_STOCK_POSITION_INFO"  # 推送持仓信息
PUSH_STOCK_BOARD_INFO = "PUSH_STOCK_BOARD_INFO"  # 推送股票板块信息
PUSH_STOCK_CONCEPT_INFO = "PUSH_STOCK_CONCEPT_INFO"  # 推送股票概念信息
PUSH_HIT_BOARD_STOCK_INFO = "PUSH_HIT_BOARD_STOCK_INFO" #推送打板股票信息
PUSH_STOCK_ETF_INFO = "PUSH_STOCK_ETF_INFO" #推送ETF信息

# 股票字段信息缓存
stock_field_info = None

# 全局线程池，最大4个线程，可根据实际压力调整
executor = ThreadPoolExecutor(max_workers=16)

'''
2025-07-10 23:53:45 | INFO     | main | trade.log.YfyLog:<lambda>:122 - [yfydata_redisData.py:65] 获取股票字段信息: [{'fieldType': '1', 'fieldName': 'symbol', 'remark': '股票代码'}, {'fieldType': '2', 'fieldName': 'name', 'remark': '股票名称'}, {'fieldType': '3', 'fieldName': 'open', 'remark': '开盘价'}, {'fieldType': '4', 'fieldName': 'close', 'remark': '收盘价'}, {'fieldType': '5', 'fieldName': 'high', 'remark': '最高价'}, {'fieldType': '6', 'fieldName': 'low', 'remark': '最低价'}, {'fieldType': '7', 'fieldName': 'turnOverRate', 'remark': '换手率'}, {'fieldType': '8', 'fieldName': 'openChangePercent', 'remark': '开盘价涨跌幅'}, {'fieldType': '9', 'fieldName': 'changeRate', 'remark': '涨速'}, {'fieldType': '10', 'fieldName': 'changePercent', 'remark': '涨跌幅'}, {'fieldType': '11', 'fieldName': 'changePercent5M', 'remark': '5分钟涨跌幅'}, {'fieldType': '12', 'fieldName': 'changePercent3D', 'remark': '3日涨跌幅'}, {'fieldType': '13', 'fieldName': 'changePercent5D', 'remark': '5日涨跌幅'}, {'fieldType': '14', 'fieldName': 'changePercent10D', 'remark': '10日涨跌幅'}, {'fieldType': '15', 'fieldName': 'changePercent20D', 'remark': '20日涨跌幅'}, {'fieldType': '16', 'fieldName': 'changePercent30D', 'remark': '30日涨跌幅'}, {'fieldType': '17', 'fieldName': 'changePercent60D', 'remark': '60日涨跌幅'}, {'fieldType': '18', 'fieldName': 'volume', 'remark': '成交量'}, {'fieldType': '19', 'fieldName': 'volumeAmount', 'remark': '成交额'}, {'fieldType': '20', 'fieldName': 'volumeMin', 'remark': '分钟量'}, {'fieldType': '21', 'fieldName': 'boardRank', 'remark': '板块排名'}, {'fieldType': '22', 'fieldName': 'boardMainInFlowAmount', 'remark': '板块主力净流入额'}, {'fieldType': '23', 'fieldName': 'boardInFlowRank', 'remark': '板块主净排名'}, {'fieldType': '24', 'fieldName': 'boardOpen', 'remark': '板块开盘价'}, {'fieldType': '25', 'fieldName': 'boardClose', 'remark': '板块收盘价'}, {'fieldType': '26', 'fieldName': 'boardHigh', 'remark': '板块最高价'}, {'fieldType': '27', 'fieldName': 'boardLow', 'remark': '板块最低价'}, {'fieldType': '28', 'fieldName': 'boardChangePercent', 'remark': '板块涨跌幅'}, {'fieldType': '29', 'fieldName': 'boardChangePercentRank', 'remark': '板块涨跌幅排名'}, {'fieldType': '30', 'fieldName': 'boardVolumeAmountRank', 'remark': '板块成交额排名'}, {'fieldType': '31', 'fieldName': 'boardMainInFlowAmountRank', 'remark': '板块主净排名'}, {'fieldType': '32', 'fieldName': 'totalLimitUpCount', 'remark': '5日涨停数'}, {'fieldType': '33', 'fieldName': 'totalLimitDownCount', 'remark': '5日跌停数'}, {'fieldType': '34', 'fieldName': 'isLimitUp', 'remark': '是否涨停'}, {'fieldType': '35', 'fieldName': 'isLimitDown', 'remark': '是否跌停'}, {'fieldType': '36', 'fieldName': 'ma5', 'remark': 'MA5'}, {'fieldType': '37', 'fieldName': 'ma10', 'remark': 'MA10'}, {'fieldType': '38', 'fieldName': 'ma20', 'remark': 'MA20'}, {'fieldType': '39', 'fieldName': 'ma60', 'remark': 'MA60'}, {'fieldType': '40', 'fieldName': 'ma250', 'remark': 'MA250'}, {'fieldType': '41', 'fieldName': 'ema21', 'remark': 'EMA21'}, {'fieldType': '42', 'fieldName': 'ema55', 'remark': 'EMA55'}, {'fieldType': '43', 'fieldName': 'ema89', 'remark': 'EMA89'}, {'fieldType': '44', 'fieldName': 'ema144', 'remark': 'EMA144'}, {'fieldType': '45', 'fieldName': 'rank', 'remark': '个股排名'}, {'fieldType': '46', 'fieldName': 'mainInFlowAmount', 'remark': '个股主净'}, {'fieldType': '47', 'fieldName': 'mainInFlowRank', 'remark': '个股主净排名'}, {'fieldType': '48', 'fieldName': 'xlMainInFlowAmount', 'remark': '新浪主净'}, {'fieldType': '49', 'fieldName': 'xl_mainInFlowRank', 'remark': '新浪主净排名'}, {'fieldType': '50', 'fieldName': 'kplMainInFlowAmount', 'remark': '开盘啦主净'}, {'fieldType': '51', 'fieldName': 'kpl_mainInFlowRank', 'remark': '开盘啦主净排名'}, {'fieldType': '52', 'fieldName': 'lianbanCount', 'remark': '连板数'}, {'fieldType': '53', 'fieldName': 'conceptName', 'remark': '概念名称'}, {'fieldType': '54', 'fieldName': 'conceptOpen', 'remark': '概念开盘价'}, {'fieldType': '55', 'fieldName': 'conceptClose', 'remark': '概念收盘价'}, {'fieldType': '56', 'fieldName': 'conceptHigh', 'remark': '概念最高价'}, {'fieldType': '57', 'fieldName': 'conceptLow', 'remark': '概念最低价'}, {'fieldType': '58', 'fieldName': 'conceptChangePercent', 'remark': '概念涨跌幅'}, {'fieldType': '59', 'fieldName': 'conceptRank', 'remark': '概念排名'}, {'fieldType': '60', 'fieldName': 'conceptMainInFlowAmount', 'remark': '概念主净'}, {'fieldType': '61', 'fieldName': 'conceptInFlowRank', 'remark': '概念主净排名'}, {'fieldType': '62', 'fieldName': 'conceptSpecialName', 'remark': '概异名称'}, {'fieldType': '63', 'fieldName': 'conceptChangePercentRank', 'remark': '概念个股涨跌幅排名'}, {'fieldType': '64', 'fieldName': 'conceptVolumeAmountRank', 'remark': '概念个股成交额排名'}, {'fieldType': '65', 'fieldName': 'conceptMainInFlowAmountRank', 'remark': '概念个股主净排名'}, {'fieldType': '66', 'fieldName': 'buy_signal_list', 'remark': '买信号列表'}, {'fieldType': '67', 'fieldName': 'sell_signal_list', 'remark': '卖信号列表'}, {'fieldType': '68', 'fieldName': 'txMainInFlowAmount', 'remark': '腾讯主净'}, {'fieldType': '69', 'fieldName': 'txMainInFlowRank', 'remark': '腾讯主净排名'}, {'fieldType': '70', 'fieldName': 'ma3', 'remark': 'MA3'}, {'fieldType': '71', 'fieldName': 'hhv3', 'remark': 'HHV3'}, {'fieldType': '72', 'fieldName': 'hhv5', 'remark': 'HHV5'}, {'fieldType': '73', 'fieldName': 'hhv10', 'remark': 'HHV10'}, {'fieldType': '74', 'fieldName': 'hhv20', 'remark': 'HHV20'}, {'fieldType': '75', 'fieldName': 'llv3', 'remark': 'LLV3'}, {'fieldType': '76', 'fieldName': 'llv5', 'remark': 'LLV5'}, {'fieldType': '77', 'fieldName': 'llv10', 'remark': 'LLV10'}, {'fieldType': '78', 'fieldName': 'llv20', 'remark': 'LLV20'}, {'fieldType': '79', 'fieldName': 'changePercent120D', 'remark': '120日涨跌幅'}, {'fieldType': '80', 'fieldName': 'changePercent250D', 'remark': '250日涨跌幅'}]
'''

def get_sotck_field_info(payload=None, headers=None):
    url = yfy_b_data.get_base_url() + "field/info"
    if headers is None:
        headers = {"Content-Type": "application/json"}
    if payload is None:
        payload = {}
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()
        j_data = response.json()
        j_data_list = j_data.get("data", [])
        f_data = {item["fieldType"]: item["fieldName"] for item in j_data_list}
        #with open("stock_field.json", "w") as f:
        #    json.dump(j_data_list, f, ensure_ascii=False, indent=4)
        yfyLog.logger.info(f"获取股票字段信息: {j_data_list}")
        return f_data
    except Exception as e:
        yfyLog.logger.error(f"请求失败: {e}")
        return False

stock_field_info = get_sotck_field_info()

def handle_all_stock_info_updates(channel, data):
    executor.submit(_handle_all_stock_info_updates_impl, channel, data)

def _handle_all_stock_info_updates_impl(channel, data):
    """处理所有股票信息更新消息
    Args:
        channel: 频道名称
        data: 消息数据，应为包含股票信息的JSON数组
    Returns:
        pd.DataFrame: 包含股票信息的DataFrame
    """
    global stock_field_info
    now = datetime.datetime.now()
    date_str = now.strftime("%Y%m%d")
    try:
        yfyLog.logger.info(f"收到所有股票信息更新，频道: {channel}")
        
        compressed_bytes = base64.b64decode(data)
        decompressed_bytes = gzip.decompress(compressed_bytes)
        # 打印压缩前后数据大小
        print(f"压缩后大小: {len(compressed_bytes)/1024} KB，解压后大小: {len(decompressed_bytes)/1024} KB")
        text = decompressed_bytes.decode('utf-8')
        j_text = json.loads(text)
        # 用pandas批量映射字段名，提升大数据量处理速度
        df = pd.DataFrame(j_text)
        df.rename(columns=stock_field_info, inplace=True)
        df['symbol'] = df['symbol'].astype(str)
        # 将所有数值列四舍五入到小数点后两位
        numeric_columns = df.select_dtypes(include=['float64', 'float32', 'int64', 'int32']).columns
        df[numeric_columns] = df[numeric_columns].round(2)

        yfydm.update_all_stock(df)

        '''
        with open("all_stock_info_" + datetime.datetime.now().strftime("%Y%m%d%H%M%S") + ".json", "w") as f:
            json.dump(df.to_dict(orient='records'), f, ensure_ascii=False, indent=4)
        '''

        ############
        is_word_day, is_opening, is_Bidding = dateMgr.is_open_time()
        if not is_opening:
            # 优化：使用批量操作减少网络往返次数
            redis = StockRedisMgr()
            batch_data = {
                RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_INFO: df.to_dict(orient='records'),
                RedisKeyPrefix.OTHERS_CACHE.value + ':' + date_str + ':' + PUSH_STOCK_INFO: j_text
            }
            redis.batch_set(batch_data)
        return True
    except Exception as e:
        yfyLog.logger.error(f"处理股票信息数据时发生异常: {e}")
        yfyLog.logger.error(traceback.format_exc())
        return False


def handle_stock_board_info_updates(channel, data):
    executor.submit(_handle_stock_board_info_updates_impl, channel, data)

def _handle_stock_board_info_updates_impl(channel, data):
    now = datetime.datetime.now()
    date_str = now.strftime("%Y%m%d")
    try:
        yfyLog.logger.info(f"收到股票板块信息更新，频道：{channel}，数据类型：{len(data)}，{datetime.datetime.now()}")
        # 解压缩
        try:
            compressed_bytes = base64.b64decode(data)
            decompressed_bytes = gzip.decompress(compressed_bytes)
            # 打印压缩前后数据大小
            print(f"压缩后大小: {len(compressed_bytes)} bytes，解压后大小: {len(decompressed_bytes)} bytes")
            text = decompressed_bytes.decode('utf-8')
            j_text = json.loads(text)
            yfydm.update_all_board(j_text)

            '''
            with open("all_board_info_" + datetime.datetime.now().strftime("%Y%m%d%H%M%S") + ".json", "w") as f:
                json.dump(j_text, f, ensure_ascii=False, indent=4)
            '''
            #############
            is_word_day, is_opening, is_Bidding = dateMgr.is_open_time()
            if not is_opening:
                # 优化：使用批量操作减少网络往返次数
                redis = StockRedisMgr()
                batch_data = {
                    RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_BOARD_INFO: j_text,
                    RedisKeyPrefix.OTHERS_CACHE.value + ':' + date_str + ':' + PUSH_STOCK_BOARD_INFO: j_text
                }
                redis.batch_set(batch_data)
        except Exception as e:
            yfyLog.logger.error(f"gzip解压失败: {e}")
            return False
        return True
    except Exception as e:
        yfyLog.logger.error(f"处理股票板块信息数据时发生异常: {e}")
        yfyLog.logger.error(traceback.format_exc())
        return False


def handle_stock_concept_info_updates(channel, data):
    executor.submit(_handle_stock_concept_info_updates_impl, channel, data)

def _handle_stock_concept_info_updates_impl(channel, data):
    now = datetime.datetime.now()
    date_str = now.strftime("%Y%m%d")
    try:
        yfyLog.logger.info(f"收到股票概念信息更新，频道：{channel}，数据类型：{len(data)}，{datetime.datetime.now()}")
        try:
            compressed_bytes = base64.b64decode(data)
            decompressed_bytes = gzip.decompress(compressed_bytes)
            # 打印压缩前后数据大小
            print(f"压缩后大小: {len(compressed_bytes)} bytes，解压后大小: {len(decompressed_bytes)} bytes")
            text = decompressed_bytes.decode('utf-8')
            j_text = json.loads(text)
            yfydm.update_all_concept(j_text)

            '''
            with open("all_concept_info_" + datetime.datetime.now().strftime("%Y%m%d%H%M%S") + ".json", "w") as f:
                json.dump(j_text, f, ensure_ascii=False, indent=4)
            '''
            ##########
            is_word_day, is_opening, is_Bidding = dateMgr.is_open_time()
            if not is_opening:
                # 优化：使用批量操作减少网络往返次数
                redis = StockRedisMgr()
                batch_data = {
                    RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_CONCEPT_INFO: j_text,
                    RedisKeyPrefix.OTHERS_CACHE.value + ':' + date_str + ':' + PUSH_STOCK_CONCEPT_INFO: j_text
                }
                redis.batch_set(batch_data)
        except Exception as e:
            yfyLog.logger.error(f"gzip解压失败: {e}")
            return False
        return True
    except Exception as e:
        yfyLog.logger.error(f"处理股票概念信息数据时发生异常: {e}")
        yfyLog.logger.error(traceback.format_exc())
        return False


def handle_stock_position_info_updates(channel, data):
    executor.submit(_handle_stock_position_info_updates_impl, channel, data)

def _handle_stock_position_info_updates_impl(channel, data):
    now = datetime.datetime.now()
    try:
        yfyLog.logger.info(f"收到持仓信息更新，频道：{channel}，数据：{len(data)}，{datetime.datetime.now()}")
        yfyam.update_account_info(data)
        ############
        is_word_day, is_opening, is_Bidding = dateMgr.is_open_time()
        if not is_opening:
            redis = StockRedisMgr()
            redis.set_value(RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_POSITION_INFO + ':' + str(data["account"]), data)
        return True
    except Exception as e:
        yfyLog.logger.error(f"处理持仓信息数据时发生异常: {e}")
        yfyLog.logger.error(traceback.format_exc())
        return False

def handle_hit_board_stock_info_updates(channel, data):
    executor.submit(_handle_hit_board_stock_info_updates, channel, data)

def _handle_hit_board_stock_info_updates(channel, data):
    now = datetime.datetime.now()
    try:
        yfyLog.logger.info(f"收到打板股票数据信息，频道：{channel}，数据：{len(data)}，{datetime.datetime.now()}")
        # 添加数据类型验证
        if not isinstance(data, list):
            yfyLog.logger.error(f"打板股票数据格式错误，期望list类型，实际收到: {type(data)}")
            return False
        '''
        cmd_n1 = f" yfy#股票#排名#1-5300#号码=60+00#号码=-ST#涨跌幅>7%#涨停数=4<2#涨停数=10<3#运算=分钟量>分钟5量*1.2#运算=分钟量<分钟5量*30#10日涨跌<40%#30日涨跌<65%#60日涨跌<60%#最高价>开盘价#运算=最高价>最低价*1.01#运算=当前价>最高价*0.992"
        des_data1 = {"subscribeCmdName": cmd_n1}
        cmd_df1 = yfydm.get_data(yfydm.STOCK_LIST, des_data1)

        list_a = cmd_df1["symbol"].tolist() 
        list_b = data
    
        # 找出在list_a中但不在list_b中的元素
        only_in_a = list(set(list_a) - set(list_b))
        # 找出在list_b中但不在list_a中的元素
        only_in_b = list(set(list_b) - set(list_a))
        
        print("仅在指令命令结果中的股票:", only_in_a)
        print("仅在redis推送中的股票:", only_in_b)
        '''
        yfydm.hit_board_stock_list = data
         ############
        is_word_day, is_opening, is_Bidding = dateMgr.is_open_time()
        if is_opening:
            redis = StockRedisMgr()
            redis.set_value(RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_HIT_BOARD_STOCK_INFO, data)
        return True
    except Exception as e:
        yfyLog.logger.error(f"处理打板股票数据时发生异常: {e}")
        yfyLog.logger.error(traceback.format_exc())
        return False

def handle_stock_etf_info_updates(channel, data):
    executor.submit(_handle_stock_etf_info_updates, channel, data)

def _handle_stock_etf_info_updates(channel, data):
    now = datetime.datetime.now()
    date_str = now.strftime("%Y%m%d")
    try:
        yfyLog.logger.info(f"收到ETF信息更新，频道：{channel}，数据类型：{len(data)}，{datetime.datetime.now()}")
        try:
            compressed_bytes = base64.b64decode(data)
            decompressed_bytes = gzip.decompress(compressed_bytes)
            # 打印压缩前后数据大小
            print(f"压缩后大小: {len(compressed_bytes)} bytes，解压后大小: {len(decompressed_bytes)} bytes")
            text = decompressed_bytes.decode('utf-8')
            j_text = json.loads(text)
            yfydm.update_all_eft(j_text)

            #yfyLog.print_df(yfydm.all_etf_df.head(100))
            ##########
            is_word_day, is_opening, is_Bidding = dateMgr.is_open_time()
            if not is_opening:
                # 优化：使用批量操作减少网络往返次数
                redis = StockRedisMgr()
                batch_data = {
                    RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_ETF_INFO: j_text,
                    RedisKeyPrefix.OTHERS_CACHE.value + ':' + date_str + ':' + PUSH_STOCK_ETF_INFO: j_text
                }
                redis.batch_set(batch_data)
        except Exception as e:
            yfyLog.logger.error(f"gzip解压失败: {e}")
            return False
        return True
    except Exception as e:
        yfyLog.logger.error(f"处理股票概念信息数据时发生异常: {e}")
        yfyLog.logger.error(traceback.format_exc())
        return False

def setup():
    redis = StockRedisMgr()
    # 取回来初始化
    data = redis.get_value(RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_INFO)
    if data:
        df = pd.DataFrame(data)
        yfydm.update_all_stock(df)
    data = redis.get_value(RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_BOARD_INFO)
    if data:
        yfydm.update_all_board(data)
    data = redis.get_value(RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_CONCEPT_INFO)
    if data:
        yfydm.update_all_concept(data)
    data = redis.get_value(RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_ETF_INFO)
    if data:
        yfydm.update_all_eft(data)
    # 取回所有账户的持仓缓存
    pattern = RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_STOCK_POSITION_INFO + ':*'
    keys = redis.keys(pattern)
    for k in keys:
        acc_data = redis.get_value(k)
        if acc_data:
            yfyam.update_account_info(acc_data)
    data = redis.get_value(RedisKeyPrefix.OTHERS_CACHE.value + ':' + PUSH_HIT_BOARD_STOCK_INFO)
    if data:
        yfydm.hit_board_stock_list = data


def init():
    """初始化Redis订阅服务"""
    setup()
    # 创建订阅者实例（使用单例模式）
    subscriber = RedisSubscriber()
    # 注册回调函数 - 使用实际的频道名称
    subscriber.register_callback(PUSH_STOCK_INFO, handle_all_stock_info_updates)  # 股票信息
    subscriber.register_callback(PUSH_STOCK_POSITION_INFO, handle_stock_position_info_updates)  # 持仓信息
    subscriber.register_callback(PUSH_STOCK_BOARD_INFO, handle_stock_board_info_updates)  # 股票板块信息
    subscriber.register_callback(PUSH_STOCK_CONCEPT_INFO, handle_stock_concept_info_updates)  # 股票概念信息
    subscriber.register_callback(PUSH_HIT_BOARD_STOCK_INFO, handle_hit_board_stock_info_updates)  # 推送打板股票信息
    subscriber.register_callback(PUSH_STOCK_ETF_INFO, handle_stock_etf_info_updates)  # 推送打板股票信息
    
    # 订阅频道
    channels_to_subscribe = [
        PUSH_STOCK_INFO,
        PUSH_STOCK_POSITION_INFO, 
        PUSH_STOCK_BOARD_INFO,
        PUSH_STOCK_CONCEPT_INFO,
        PUSH_HIT_BOARD_STOCK_INFO,
        PUSH_STOCK_ETF_INFO
    ]
    subscriber.subscribe(channels_to_subscribe)
    
    # 启动监听
    subscriber.start_listening()
    yfyLog.logger.info("Redis订阅服务已初始化并启动")
    return subscriber


def shutdown():
    """关闭Redis订阅服务"""
    subscriber = RedisSubscriber()
    subscriber.stop_listening()
    subscriber.unsubscribe()
    yfyLog.logger.info("Redis订阅服务已关闭")


if __name__ == "__main__":
    # 初始化Redis订阅服务
    subscriber = init()
    
    # 保持程序运行，等待消息
    try:
        print("Redis订阅服务已启动，等待接收消息...按Ctrl+C退出")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n用户中断，关闭服务...")
        # 关闭Redis订阅服务
        shutdown()