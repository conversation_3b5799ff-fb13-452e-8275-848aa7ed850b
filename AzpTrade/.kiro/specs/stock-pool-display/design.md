# Design Document

## Overview

The Stock Pool Display feature will create a new web interface that integrates with the existing StockPoolFactory to display stocks from various screening pools. The system will follow the same architectural patterns as the existing stock_ma.html page, using a Flask service backend and Vue.js frontend with Element Plus components.

## Architecture

### Backend Architecture

```
stockMonitorWeb.py
├── StockPoolDisplayService (new)
│   ├── get_stock_pool_data()
│   ├── get_available_pools()
│   └── format_stock_data()
├── API Routes (new)
│   ├── /api/stock-pool-data
│   ├── /api/stock-pool-list
│   └── /stock_pool_display.html
└── StockPoolFactory Integration
    ├── func_dict access
    ├── get_stock_pool() calls
    └── Pool data aggregation
```

### Frontend Architecture

```
stock_pool_display.html
├── Vue.js Application
│   ├── Data Management
│   │   ├── poolData (reactive)
│   │   ├── availablePools (reactive)
│   │   └── filteredData (computed)
│   ├── Components
│   │   ├── Pool Statistics Cards
│   │   ├── Filter Controls
│   │   ├── Data Table
│   │   └── Export Functionality
│   └── Services
│       ├── Data Fetching
│       ├── Auto-refresh
│       └── Export Generation
```

## Components and Interfaces

### 1. StockPoolDisplayService

**Purpose:** Backend service to fetch and format stock pool data

**Key Methods:**
```python
class StockPoolDisplayService:
    def __init__(self):
        self.pool_factory = StockPoolFactory
        self.last_update = None
        self.cached_data = {}
    
    def get_stock_pool_data(self, selected_pools=None):
        """Fetch stocks from specified pools or all pools"""
        # Returns formatted stock data with pool source information
        
    def get_available_pools(self):
        """Get list of available stock pools with metadata"""
        # Returns pool names, descriptions, and stock counts
        
    def format_stock_data(self, raw_data, pool_source):
        """Format raw stock data for frontend consumption"""
        # Standardizes data format and adds pool source info
```

### 2. API Endpoints

**GET /api/stock-pool-data**
- Query Parameters: `pools` (optional, comma-separated pool names)
- Response: JSON array of formatted stock data
- Caching: 3-minute cache with auto-refresh

**GET /api/stock-pool-list**
- Response: JSON array of available pools with metadata
- Format: `[{name, displayName, count, description}]`

**GET /stock_pool_display.html**
- Serves the main HTML page
- Includes navigation integration

### 3. Frontend Components

**Pool Statistics Component:**
```javascript
// Displays top pools by stock count
const PoolStatistics = {
  props: ['pools', 'selectedPools'],
  emits: ['pool-selected'],
  template: `
    <div class="pool-stats">
      <div v-for="pool in topPools" class="pool-card">
        <el-checkbox v-model="selectedPools" :label="pool.name">
          {{ pool.displayName }} ({{ pool.count }})
        </el-checkbox>
      </div>
    </div>
  `
}
```

**Stock Data Table:**
```javascript
// Main data display table
const StockTable = {
  props: ['stockData', 'loading'],
  emits: ['sort-change'],
  // Uses el-table with sortable columns
  // Includes color coding and formatting
}
```

## Data Models

### Stock Data Model
```javascript
{
  symbol: "000001",           // 股票代码
  name: "平安银行",           // 股票名称
  close: 12.34,              // 当前价
  changePercent: 2.15,       // 涨跌幅
  volumeAmount: 1234567890,  // 成交额
  volume_ratio: 1.25,        // 量比
  mainInFlowAmount: 12345678, // 主力净流入
  boardName: "银行",         // 板块
  conceptName: "金融科技",    // 概念
  circleMarketValue: 123456789, // 流通市值
  poolSources: ["排名", "买信号"], // 股池来源
  lastUpdate: "2024-01-15 14:30:00" // 最后更新时间
}
```

### Pool Metadata Model
```javascript
{
  name: "PAIMING",           // 内部名称
  displayName: "排名",       // 显示名称
  count: 156,               // 股票数量
  description: "综合排名靠前的股票", // 描述
  lastUpdate: "2024-01-15 14:30:00" // 最后更新
}
```

## Error Handling

### Backend Error Handling
```python
try:
    pool_data = self.get_pool_stocks(pool_name)
    return self.format_response(pool_data)
except Exception as e:
    logger.error(f"Error fetching pool {pool_name}: {str(e)}")
    return {"error": f"Failed to fetch pool data: {str(e)}"}
```

### Frontend Error Handling
```javascript
const fetchPoolData = async () => {
  try {
    loading.value = true
    const response = await fetch('/api/stock-pool-data')
    if (!response.ok) throw new Error('Network error')
    stockData.value = await response.json()
  } catch (error) {
    ElMessage.error('获取股池数据失败: ' + error.message)
    stockData.value = []
  } finally {
    loading.value = false
  }
}
```

## Testing Strategy

### Unit Tests
1. **StockPoolDisplayService Tests**
   - Test pool data fetching from StockPoolFactory
   - Test data formatting and aggregation
   - Test error handling for missing pools
   - Test caching mechanism

2. **API Endpoint Tests**
   - Test successful data retrieval
   - Test query parameter filtering
   - Test error responses
   - Test response format validation

### Integration Tests
1. **StockPoolFactory Integration**
   - Test access to all available pools
   - Test data consistency across pools
   - Test performance with large datasets

2. **Frontend-Backend Integration**
   - Test API data consumption
   - Test real-time updates
   - Test export functionality

### UI Tests
1. **Component Testing**
   - Test table sorting and filtering
   - Test pool selection functionality
   - Test responsive design
   - Test export image generation

2. **User Experience Testing**
   - Test loading states
   - Test error message display
   - Test navigation integration

## Implementation Considerations

### Performance Optimization
1. **Data Caching:** Implement 3-minute cache for pool data to reduce StockPoolFactory calls
2. **Lazy Loading:** Load pool data on-demand when pools are selected
3. **Pagination:** Consider pagination for large datasets (>1000 stocks)
4. **Debounced Updates:** Debounce filter changes to prevent excessive API calls

### Security Considerations
1. **Input Validation:** Validate pool names and query parameters
2. **Rate Limiting:** Implement rate limiting for API endpoints
3. **Error Information:** Avoid exposing internal system details in error messages

### Scalability Considerations
1. **Pool Management:** Design for easy addition of new stock pools
2. **Data Format:** Use flexible data format to accommodate new stock attributes
3. **API Versioning:** Consider API versioning for future enhancements

### Browser Compatibility
1. **Modern Browsers:** Target Chrome 80+, Firefox 75+, Safari 13+
2. **Responsive Design:** Support desktop and tablet viewports
3. **Progressive Enhancement:** Ensure basic functionality without JavaScript

## Integration Points

### StockPoolFactory Integration
```python
# Access existing pool functions
from trade.strategy.StockPoolFactory import func_dict, get_stock_pool

# Get available pools
available_pools = list(func_dict.keys())

# Fetch pool data
for pool_name, pool_func in func_dict.items():
    try:
        pool_stocks = pool_func()  # Execute pool function
        formatted_data = self.format_pool_data(pool_stocks, pool_name)
        aggregated_data.extend(formatted_data)
    except Exception as e:
        logger.error(f"Error fetching {pool_name}: {e}")
```

### Navigation Integration
```html
<!-- Add to existing navigation in stockMonitorWeb.py -->
<a href="/stock_pool_display.html" class="nav-link" target="_blank">股池总览</a>
```

### Service Registration
```python
# Add to register_services() in stockMonitorWeb.py
service_manager.register_service('stock_pool_display_service', StockPoolDisplayService)
```