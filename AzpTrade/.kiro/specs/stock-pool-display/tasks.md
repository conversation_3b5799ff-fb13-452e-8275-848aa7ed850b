# Implementation Plan

- [ ] 1. Create StockPoolDisplayService backend service
  - Create new service class in trade/html/services/stock_pool_display_service.py
  - Implement get_stock_pool_data() method to fetch data from StockPoolFactory
  - Implement get_available_pools() method to list all available pools
  - Implement format_stock_data() method to standardize data format
  - Add error handling and logging for pool data fetching
  - Implement caching mechanism with 3-minute refresh interval
  - _Requirements: 1.1, 1.2, 1.4, 5.1, 5.4_

- [ ] 2. Add API endpoints to stockMonitorWeb.py
  - Add /api/stock-pool-data endpoint with optional pool filtering
  - Add /api/stock-pool-list endpoint to return available pools metadata
  - Add /stock_pool_display.html route to serve the main page
  - Register StockPoolDisplayService in service_manager
  - Add appropriate error handling and response formatting
  - Implement query parameter validation for pool names
  - _Requirements: 1.1, 1.2, 1.3, 5.1_

- [ ] 3. Create stock_pool_display.html frontend page
  - Create new HTML file based on stock_ma.html structure
  - Set up Vue.js application with Element Plus components
  - Implement reactive data properties for pools and stock data
  - Add navigation links consistent with existing pages
  - Include necessary CSS and JavaScript dependencies
  - Set up basic page layout with header and navigation
  - _Requirements: 6.1, 6.2, 6.3_

- [ ] 4. Implement pool statistics display component
  - Create pool statistics cards showing top pools by stock count
  - Implement checkbox selection for pool filtering
  - Add hover effects and visual styling for pool cards
  - Display pool names and stock counts dynamically
  - Implement click handlers for pool selection
  - Add responsive design for different screen sizes
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 5. Create main stock data table component
  - Implement el-table with columns: 代码, 名称, 当前价, 涨跌幅, 成交额, 量比, 主力净流入, 板块, 概念, 流通值, 股池来源
  - Add sortable functionality for all numeric columns
  - Implement color coding for positive/negative values (red/green)
  - Add clickable stock code links to external stock pages
  - Format large numbers appropriately (亿 for hundreds of millions)
  - Handle missing data with appropriate placeholders
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.6_

- [ ] 6. Implement stock pool source column display
  - Add "股池来源" column to show which pools each stock belongs to
  - Handle stocks that belong to multiple pools with appropriate styling
  - Implement color coding for different pool sources
  - Add truncation with ellipsis for long pool names
  - Show full pool names on hover with tooltips
  - Highlight selected pools in the source column when filtering
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 7. Add filtering and sorting functionality
  - Implement pool selection filters with checkbox controls
  - Add "Clear Filters" button to reset all selections
  - Show "(已筛选)" indicator when filters are active
  - Maintain sort order during data refreshes
  - Implement computed property for filtered data
  - Add stock count display with filter status
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [ ] 8. Implement data fetching and auto-refresh
  - Create fetchPoolData() function to call API endpoints
  - Implement 3-minute auto-refresh interval
  - Add loading states and indicators during data fetching
  - Handle API errors with user-friendly messages
  - Implement data caching on frontend to reduce API calls
  - Update last update time display
  - _Requirements: 1.5, 6.4, 6.5_

- [ ] 9. Add export functionality
  - Implement exportStockList() function similar to stock_ma.html
  - Create temporary export table for image generation
  - Handle large datasets by splitting into multiple parts (>100 stocks)
  - Include export title with date and filter status
  - Generate and download JPEG images automatically
  - Add loading state for export button
  - Handle export errors with appropriate messaging
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 10. Add styling and responsive design
  - Apply consistent styling with existing pages (colors, fonts, layout)
  - Implement responsive design for different screen sizes
  - Add hover effects and transitions for interactive elements
  - Style pool statistics cards with appropriate spacing and colors
  - Ensure table is properly sized and scrollable
  - Add loading spinners and empty state styling
  - _Requirements: 6.2, 6.3_

- [ ] 11. Implement error handling and edge cases
  - Add try-catch blocks for all API calls
  - Handle empty pool data with appropriate messages
  - Display user-friendly error messages for network failures
  - Handle missing or invalid pool names gracefully
  - Add fallback behavior when StockPoolFactory functions fail
  - Implement proper cleanup for intervals and event listeners
  - _Requirements: 5.5, 6.5_

- [ ] 12. Add navigation integration and testing
  - Add navigation link to stock_pool_display.html in existing pages
  - Test integration with existing navigation structure
  - Verify page loads correctly and data displays properly
  - Test all filtering and sorting functionality
  - Test export functionality with various data sizes
  - Verify responsive design on different screen sizes
  - Test error scenarios and recovery
  - _Requirements: 6.1, 1.1, 2.1, 4.1_