# Requirements Document

## Introduction

This feature will create a new web page to display stock data from various stock pools managed by the StockPoolFactory. The page will provide a comprehensive view of stocks from different pools (like 趋势, 买信号, 排名, etc.) with filtering capabilities and a similar UI/UX to the existing stock_ma.html page.

## Requirements

### Requirement 1

**User Story:** As a trader, I want to view stocks from different stock pools in a unified interface, so that I can analyze and compare stocks across various screening strategies.

#### Acceptance Criteria

1. WHEN the user accesses the stock pool display page THEN the system SHALL display a list of all available stock pools from StockPoolFactory
2. WHEN the user selects one or more stock pools THEN the system SHALL display stocks from the selected pools in a table format
3. WHEN no pools are selected THEN the system SHALL display stocks from all pools by default
4. WHEN the page loads THEN the system SHALL show the total count of stocks displayed
5. WHEN stock data is updated THEN the system SHALL refresh the display automatically every 3 minutes

### Requirement 2

**User Story:** As a trader, I want to filter and sort stock pool data, so that I can focus on the most relevant stocks for my trading strategy.

#### Acceptance Criteria

1. WHEN the user clicks on a stock pool filter THEN the system SHALL show only stocks from that specific pool
2. WHEN multiple pools are selected THEN the system SHALL display stocks from all selected pools
3. WHEN the user clicks "Clear Filters" THEN the system SHALL reset all filters and show all stocks
4. WHEN the user clicks on column headers THEN the system SHALL sort the table by that column
5. WHEN sorting is applied THEN the system SHALL maintain the sort order during data refreshes
6. WHEN filters are active THEN the system SHALL display "(已筛选)" indicator next to the stock count

### Requirement 3

**User Story:** As a trader, I want to see comprehensive stock information in a table format, so that I can make informed trading decisions.

#### Acceptance Criteria

1. WHEN stocks are displayed THEN the system SHALL show columns for: 代码, 名称, 当前价, 涨跌幅, 成交额, 量比, 主力净流入, 板块, 概念, 流通值, 股池来源
2. WHEN displaying stock codes THEN the system SHALL make them clickable links to external stock pages
3. WHEN displaying prices and percentages THEN the system SHALL use appropriate color coding (red for positive, green for negative)
4. WHEN displaying large numbers THEN the system SHALL format them appropriately (亿 for hundreds of millions)
5. WHEN a stock belongs to multiple pools THEN the system SHALL indicate all pool sources
6. WHEN stock data is missing THEN the system SHALL display "-" or appropriate placeholder

### Requirement 4

**User Story:** As a trader, I want to export stock pool data, so that I can save and share the information for further analysis.

#### Acceptance Criteria

1. WHEN the user clicks the export button THEN the system SHALL generate an image of the current stock list
2. WHEN there are more than 100 stocks THEN the system SHALL split the export into multiple parts
3. WHEN exporting THEN the system SHALL include the export title with date and filter status
4. WHEN export is complete THEN the system SHALL automatically download the image file(s)
5. WHEN export fails THEN the system SHALL display an appropriate error message

### Requirement 5

**User Story:** As a trader, I want to see real-time stock pool statistics, so that I can understand the distribution of stocks across different pools.

#### Acceptance Criteria

1. WHEN the page loads THEN the system SHALL display statistics cards showing top stock pools by count
2. WHEN pool statistics are shown THEN the system SHALL display pool name and stock count
3. WHEN a statistics card is clicked THEN the system SHALL filter to show only stocks from that pool
4. WHEN statistics are displayed THEN the system SHALL update them automatically when data refreshes
5. WHEN no stocks are available THEN the system SHALL display appropriate empty state message

### Requirement 6

**User Story:** As a trader, I want the page to have consistent navigation and styling, so that it integrates seamlessly with the existing trading interface.

#### Acceptance Criteria

1. WHEN the page loads THEN the system SHALL display navigation links to other trading pages
2. WHEN styling is applied THEN the system SHALL use consistent colors, fonts, and layout with stock_ma.html
3. WHEN the page is responsive THEN the system SHALL adapt to different screen sizes appropriately
4. WHEN loading data THEN the system SHALL display loading indicators
5. WHEN errors occur THEN the system SHALL display user-friendly error messages

### Requirement 7

**User Story:** As a trader, I want to see which stock pool each stock belongs to, so that I can understand the screening criteria that identified each stock.

#### Acceptance Criteria

1. WHEN stocks are displayed THEN the system SHALL show a "股池来源" column indicating the source pool(s)
2. WHEN a stock belongs to multiple pools THEN the system SHALL display all pool names with appropriate styling
3. WHEN pool names are long THEN the system SHALL truncate them with ellipsis and show full name on hover
4. WHEN pool sources are displayed THEN the system SHALL use different colors for different pools
5. WHEN filtering by pool THEN the system SHALL highlight the selected pool in the source column