import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    # WebSocket Configuration
    WS_URL = os.getenv('WS_URL', 'wss://stock.haidian666.com/market')
    WS_RETRY_INTERVAL = int(os.getenv('WS_RETRY_INTERVAL', '5'))

    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = 'logs/trading_system.log'

    # Database Configuration
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = int(os.getenv('DB_PORT', '3306'))
    DB_USER = os.getenv('DB_USER', 'root')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    DB_NAME = os.getenv('DB_NAME', 'trading_db')

    # Trading Configuration
    TRADING_HOURS_START = os.getenv('TRADING_HOURS_START', '09:30')
    TRADING_HOURS_END = os.getenv('TRADING_HOURS_END', '15:00')

    # API Configuration
    EASTMONEY_API_URL = os.getenv('EASTMONEY_API_URL', 'http://quote.eastmoney.com/stocklist.html')

config = Config()
