#!/bin/bash

# AI看线 Web服务 - 简化管理脚本

cd "$(dirname "${BASH_SOURCE[0]}")"

case "$1" in
    ""|help)
        echo "AI看线 Web服务管理 (端口:8018)"
        echo ""
        echo "命令:"
        echo "  ./service.sh start    - 启动服务"
        echo "  ./service.sh stop     - 停止服务"
        echo "  ./service.sh restart  - 重启服务"
        echo "  ./service.sh status   - 查看状态"
        echo "  ./service.sh logs     - 查看日志"
        echo ""
        ;;
    *)
        ./start_web.sh "$1"
        ;;
esac 