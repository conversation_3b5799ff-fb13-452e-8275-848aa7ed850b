# AI看线 Nginx部署指南

## 📋 部署概览

将AI看线服务通过nginx反向代理到域名 `aikline.haidian666.com`

- **本地服务端口**: 8018
- **域名访问端口**: 80 (HTTP) / 443 (HTTPS)
- **反向代理**: nginx -> localhost:8018

## 🚀 快速部署

### 方法1: 自动安装脚本 (推荐)

```bash
# 1. 确保AI看线服务正在运行
./service.sh start

# 2. 运行nginx自动安装脚本
sudo ./setup_nginx.sh

# 3. 根据提示配置SSL证书
```

### 方法2: 手动部署

```bash
# 1. 安装nginx
sudo apt update
sudo apt install nginx

# 2. 复制配置文件
sudo cp nginx.conf /etc/nginx/sites-available/aikline.haidian666.com
sudo ln -sf /etc/nginx/sites-available/aikline.haidian666.com /etc/nginx/sites-enabled/

# 3. 测试配置
sudo nginx -t

# 4. 重启nginx
sudo systemctl restart nginx
```

## 🔒 SSL证书配置

### 选项1: Let's Encrypt (推荐)

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取免费SSL证书
sudo certbot --nginx -d aikline.haidian666.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 选项2: 自签名证书 (测试用)

```bash
# 生成自签名证书
sudo ./generate_ssl.sh

# 重启nginx
sudo systemctl restart nginx
```

### 选项3: 仅HTTP (快速测试)

```bash
# 使用HTTP版本配置
sudo cp nginx-http-only.conf /etc/nginx/sites-available/aikline.haidian666.com
sudo nginx -t && sudo systemctl restart nginx
```

## 🌐 域名解析配置

确保域名 `aikline.haidian666.com` 解析到你的服务器IP：

```bash
# 检查域名解析
nslookup aikline.haidian666.com

# 应该返回你的服务器IP地址
```

## 📊 配置文件说明

### 主要配置特点

- ✅ **反向代理**: nginx -> localhost:8018
- ✅ **静态文件缓存**: CSS/JS/图片缓存1天
- ✅ **图表文件缓存**: 分析图表缓存2小时  
- ✅ **AI分析超时**: 10分钟超时适配AI分析
- ✅ **安全头部**: 防XSS、防点击劫持
- ✅ **gzip压缩**: 自动压缩传输
- ✅ **健康检查**: /health 端点

### 日志文件

- **访问日志**: `/var/log/nginx/aikline_access.log`
- **错误日志**: `/var/log/nginx/aikline_error.log`

## 🔧 管理命令

```bash
# 查看nginx状态
sudo systemctl status nginx

# 重启nginx
sudo systemctl restart nginx

# 重载配置 (不中断服务)
sudo nginx -s reload

# 测试配置语法
sudo nginx -t

# 查看实时日志
sudo tail -f /var/log/nginx/aikline_*.log

# 查看AI看线服务状态
./service.sh status
```

## 🐛 故障排除

### 常见问题

1. **502 Bad Gateway**
   ```bash
   # 检查AI看线服务是否运行
   ./service.sh status
   
   # 检查端口8018是否监听
   netstat -tlnp | grep 8018
   ```

2. **403 Forbidden**
   ```bash
   # 检查nginx配置
   sudo nginx -t
   
   # 检查文件权限
   ls -la /etc/nginx/sites-available/
   ```

3. **SSL证书错误**
   ```bash
   # 检查证书文件
   sudo ls -la /etc/ssl/certs/aikline.haidian666.com.*
   sudo ls -la /etc/ssl/private/aikline.haidian666.com.*
   
   # 重新生成证书
   sudo ./generate_ssl.sh
   ```

4. **域名无法访问**
   ```bash
   # 检查域名解析
   nslookup aikline.haidian666.com
   
   # 检查防火墙
   sudo ufw status
   ```

### 性能优化

```bash
# 查看nginx工作进程
ps aux | grep nginx

# 查看连接数统计
ss -tuln | grep :80
ss -tuln | grep :443

# 查看AI看线服务资源使用
ps aux | grep python | grep web_app
```

## 📈 监控建议

```bash
# 设置日志轮转
sudo vim /etc/logrotate.d/aikline

# 监控脚本示例
watch 'curl -s -o /dev/null -w "%{http_code}" http://aikline.haidian666.com/health'

# 监控磁盘空间 (图表文件)
du -sh AI-Kline/output/charts/
```

## 🔐 安全建议

1. **限制访问IP** (可选)
   ```nginx
   # 在location块中添加
   allow 你的IP;
   deny all;
   ```

2. **设置访问频率限制**
   ```nginx
   # 在http块中添加
   limit_req_zone $binary_remote_addr zone=aikline:10m rate=10r/m;
   
   # 在location块中添加
   limit_req zone=aikline burst=20 nodelay;
   ```

3. **定期更新证书**
   ```bash
   # 检查证书过期时间
   sudo certbot certificates
   
   # 手动续期
   sudo certbot renew
   ```

---

## 🎉 部署完成后测试

1. **基础访问测试**
   ```bash
   curl -I http://aikline.haidian666.com/health
   ```

2. **完整功能测试**
   - 访问首页: https://aikline.haidian666.com
   - 输入股票代码测试分析功能
   - 检查图表是否正常显示

3. **性能测试**
   ```bash
   # 简单压力测试
   ab -n 100 -c 10 http://aikline.haidian666.com/
   ```

恭喜！你的AI看线服务现在可以通过域名访问了！ 🎊 