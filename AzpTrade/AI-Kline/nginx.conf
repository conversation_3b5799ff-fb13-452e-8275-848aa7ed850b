server {
    listen 80;
    server_name aikline.haidian666.com;
    
    # 重定向到HTTPS (推荐)
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name aikline.haidian666.com;
    
    # SSL配置 (需要替换为实际的证书路径)
    ssl_certificate /etc/ssl/certs/aikline.haidian666.com.crt;
    ssl_certificate_key /etc/ssl/private/aikline.haidian666.com.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头部
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 设置客户端最大上传大小
    client_max_body_size 50M;
    
    # 日志配置
    access_log /var/log/nginx/aikline_access.log;
    error_log /var/log/nginx/aikline_error.log;
    
    # 静态文件缓存
    location /static/ {
        proxy_pass http://127.0.0.1:8018;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 静态文件缓存1天
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # 图表文件缓存
    location /output/charts/ {
        proxy_pass http://127.0.0.1:8018;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 图表文件缓存2小时
        expires 2h;
        add_header Cache-Control "public";
    }
    
    # 主要的反向代理配置
    location / {
        proxy_pass http://127.0.0.1:8018;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # 禁用某些页面的缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # API接口特殊处理
    location /analyze {
        proxy_pass http://127.0.0.1:8018;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间，因为AI分析可能需要较长时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
    }
    
    # 股票信息API
    location /stock_info/ {
        proxy_pass http://127.0.0.1:8018;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 股票信息可以缓存短时间
        expires 5m;
        add_header Cache-Control "public";
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "OK";
        add_header Content-Type text/plain;
    }
}

# HTTP版本配置（如果不想强制HTTPS）
server {
    listen 80;
    server_name aikline.haidian666.com;
    
    # 如果不想使用HTTPS，可以注释掉上面的重定向，启用下面的配置
    # 
    # # 安全头部
    # add_header X-Frame-Options DENY;
    # add_header X-Content-Type-Options nosniff;
    # add_header X-XSS-Protection "1; mode=block";
    # 
    # # 设置客户端最大上传大小
    # client_max_body_size 50M;
    # 
    # # 日志配置
    # access_log /var/log/nginx/aikline_access.log;
    # error_log /var/log/nginx/aikline_error.log;
    # 
    # # 主要的反向代理配置
    # location / {
    #     proxy_pass http://127.0.0.1:8018;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    #     
    #     # 超时设置
    #     proxy_connect_timeout 30s;
    #     proxy_send_timeout 300s;
    #     proxy_read_timeout 300s;
    # }
} 