#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI看线 - 缓存管理工具
"""

import os
import sys
import argparse
from datetime import datetime, timedelta

def list_cache_files(output_dir='./output'):
    """列出所有缓存文件"""
    print("📋 缓存文件列表:")
    print("=" * 60)
    
    cache_files = []
    
    # 分析结果文件
    for file in os.listdir(output_dir):
        if file.endswith('_analysis_result.txt'):
            file_path = os.path.join(output_dir, file)
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            stock_code = file.replace('_analysis_result.txt', '')
            
            # 检查对应的图表文件
            charts_dir = os.path.join(output_dir, 'charts')
            chart_count = 0
            if os.path.exists(charts_dir):
                chart_count = len([f for f in os.listdir(charts_dir) 
                                 if f.startswith(stock_code)])
            
            cache_files.append({
                'stock_code': stock_code,
                'file_path': file_path,
                'file_time': file_time,
                'chart_count': chart_count
            })
    
    if not cache_files:
        print("📭 没有找到缓存文件")
        return
    
    # 按时间排序
    cache_files.sort(key=lambda x: x['file_time'], reverse=True)
    
    print(f"{'股票代码':<10} {'生成时间':<20} {'图表数量':<8} {'状态':<10}")
    print("-" * 60)
    
    expire_time = datetime.now() - timedelta(hours=2)
    
    for cache in cache_files:
        status = "✅ 有效" if cache['file_time'] > expire_time else "⏰ 过期"
        print(f"{cache['stock_code']:<10} {cache['file_time'].strftime('%Y-%m-%d %H:%M:%S'):<20} "
              f"{cache['chart_count']:<8} {status:<10}")
    
    print(f"\n总计: {len(cache_files)} 个缓存文件")

def clean_cache(output_dir='./output', force=False):
    """清理过期缓存"""
    print("🧹 清理过期缓存...")
    print("=" * 40)
    
    expire_time = datetime.now() - timedelta(hours=2)
    cleaned_count = 0
    
    # 清理分析结果文件
    for file in os.listdir(output_dir):
        if file.endswith('_analysis_result.txt'):
            file_path = os.path.join(output_dir, file)
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            if file_time < expire_time or force:
                try:
                    os.remove(file_path)
                    print(f"🗑️ 删除: {file}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"❌ 删除失败: {file} - {e}")
    
    # 清理图表文件
    charts_dir = os.path.join(output_dir, 'charts')
    if os.path.exists(charts_dir):
        for file in os.listdir(charts_dir):
            if file.endswith('.png') or file.endswith('.html'):
                file_path = os.path.join(charts_dir, file)
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                if file_time < expire_time or force:
                    try:
                        os.remove(file_path)
                        print(f"🗑️ 删除图表: {file}")
                        cleaned_count += 1
                    except Exception as e:
                        print(f"❌ 删除失败: {file} - {e}")
    
    print(f"\n✅ 清理完成，共删除 {cleaned_count} 个文件")

def cache_info(output_dir='./output'):
    """显示缓存统计信息"""
    print("📊 缓存统计信息:")
    print("=" * 40)
    
    total_files = 0
    valid_files = 0
    expired_files = 0
    total_size = 0
    
    expire_time = datetime.now() - timedelta(hours=2)
    
    # 统计分析结果文件
    for file in os.listdir(output_dir):
        if file.endswith('_analysis_result.txt'):
            file_path = os.path.join(output_dir, file)
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            file_size = os.path.getsize(file_path)
            
            total_files += 1
            total_size += file_size
            
            if file_time > expire_time:
                valid_files += 1
            else:
                expired_files += 1
    
    # 统计图表文件
    charts_dir = os.path.join(output_dir, 'charts')
    chart_files = 0
    chart_size = 0
    
    if os.path.exists(charts_dir):
        for file in os.listdir(charts_dir):
            if file.endswith('.png') or file.endswith('.html'):
                file_path = os.path.join(charts_dir, file)
                chart_files += 1
                chart_size += os.path.getsize(file_path)
    
    total_size += chart_size
    
    print(f"分析结果文件: {total_files} 个")
    print(f"  - 有效: {valid_files} 个")
    print(f"  - 过期: {expired_files} 个")
    print(f"图表文件: {chart_files} 个")
    print(f"总占用空间: {total_size / 1024 / 1024:.2f} MB")
    print(f"缓存目录: {os.path.abspath(output_dir)}")

def main():
    parser = argparse.ArgumentParser(description='AI看线缓存管理工具')
    parser.add_argument('action', choices=['list', 'clean', 'info'], 
                       help='操作类型: list(列出), clean(清理), info(统计)')
    parser.add_argument('--force', action='store_true', 
                       help='强制清理所有缓存（包括有效缓存）')
    parser.add_argument('--output-dir', default='./output', 
                       help='输出目录路径 (默认: ./output)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.output_dir):
        print(f"❌ 输出目录不存在: {args.output_dir}")
        sys.exit(1)
    
    print(f"🔧 AI看线缓存管理工具 - {args.action.upper()}")
    print(f"📁 工作目录: {os.path.abspath(args.output_dir)}")
    print()
    
    if args.action == 'list':
        list_cache_files(args.output_dir)
    elif args.action == 'clean':
        if args.force:
            confirm = input("⚠️ 确定要清理所有缓存吗？(y/N): ")
            if confirm.lower() != 'y':
                print("❌ 操作已取消")
                sys.exit(0)
        clean_cache(args.output_dir, args.force)
    elif args.action == 'info':
        cache_info(args.output_dir)

if __name__ == "__main__":
    main() 