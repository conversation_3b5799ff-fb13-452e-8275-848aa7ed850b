<!DOCTYPE html>
<html>
<head>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            font-family: "Microsoft YaHei", Arial, sans-serif; 
        }
        .container {
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }
        /* 标题样式优化 */
        .title-text {
            font-size: 16px !important;
            font-weight: bold !important;
            padding: 15px 0 !important;
            margin-bottom: 15px !important;
        }
        /* 图例样式优化 */
        .legend {
            padding-top: 15px !important;
            display: flex !important;
            flex-wrap: wrap !important;
            justify-content: center !important;
        }
        .legend-item {
            margin: 0 10px !important;
            display: inline-flex !important;
            align-items: center !important;
        }
        /* 确保各种尺寸屏幕上不出现文字重叠 */
        @media (max-width: 768px) {
            .title-text {
                font-size: 14px !important;
            }
            .legend-item {
                margin: 0 5px !important;
            }
        }
    </style>

    <meta charset="UTF-8">
    <title>AI看线 - 英维克(002837) 技术分析</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>

    
</head>
<body >
    <div id="8b4dc3588d2648d29afbbc96838cd4dc" class="chart-container" style="width:100%; height:700px; "></div>
    <script>
        var chart_8b4dc3588d2648d29afbbc96838cd4dc = echarts.init(
            document.getElementById('8b4dc3588d2648d29afbbc96838cd4dc'), 'white', {renderer: 'canvas'});
        var option_8b4dc3588d2648d29afbbc96838cd4dc = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "candlestick",
            "name": "K\u7ebf",
            "coordinateSystem": "cartesian2d",
            "colorBy": "series",
            "legendHoverLink": true,
            "hoverAnimation": true,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "data": [
                [
                    28.93,
                    28.77,
                    28.22,
                    29.45
                ],
                [
                    29.06,
                    30.57,
                    28.08,
                    30.75
                ],
                [
                    30.85,
                    29.85,
                    29.76,
                    30.92
                ],
                [
                    30.0,
                    29.51,
                    29.23,
                    30.52
                ],
                [
                    29.46,
                    29.7,
                    28.92,
                    30.15
                ],
                [
                    30.15,
                    31.23,
                    30.08,
                    31.77
                ],
                [
                    31.68,
                    31.91,
                    30.75,
                    31.92
                ],
                [
                    32.15,
                    34.3,
                    32.08,
                    34.6
                ],
                [
                    34.85,
                    34.08,
                    33.62,
                    35.75
                ],
                [
                    33.9,
                    34.85,
                    33.9,
                    35.23
                ],
                [
                    32.55,
                    31.35,
                    31.35,
                    33.54
                ],
                [
                    29.67,
                    28.43,
                    28.2,
                    29.95
                ],
                [
                    29.08,
                    29.52,
                    28.65,
                    29.92
                ],
                [
                    30.31,
                    31.25,
                    29.77,
                    31.59
                ],
                [
                    31.27,
                    31.61,
                    30.78,
                    32.08
                ],
                [
                    31.55,
                    31.6,
                    31.08,
                    32.43
                ],
                [
                    31.45,
                    32.35,
                    31.32,
                    33.06
                ],
                [
                    32.32,
                    30.85,
                    30.77,
                    32.98
                ],
                [
                    30.81,
                    30.69,
                    30.36,
                    31.88
                ],
                [
                    32.0,
                    33.78,
                    31.98,
                    33.78
                ],
                [
                    33.78,
                    32.81,
                    32.38,
                    34.3
                ],
                [
                    32.77,
                    33.48,
                    32.62,
                    33.68
                ],
                [
                    33.05,
                    33.24,
                    32.24,
                    33.38
                ],
                [
                    34.42,
                    36.58,
                    34.38,
                    36.58
                ],
                [
                    37.15,
                    38.85,
                    36.91,
                    40.25
                ],
                [
                    37.55,
                    36.88,
                    36.58,
                    38.03
                ],
                [
                    36.91,
                    36.95,
                    35.83,
                    37.15
                ],
                [
                    36.95,
                    35.8,
                    34.85,
                    37.02
                ],
                [
                    35.23,
                    34.55,
                    33.69,
                    35.57
                ],
                [
                    34.12,
                    33.32,
                    32.92,
                    34.81
                ],
                [
                    32.78,
                    33.42,
                    32.38,
                    33.68
                ],
                [
                    33.99,
                    34.39,
                    33.38,
                    34.42
                ],
                [
                    34.92,
                    35.61,
                    34.79,
                    36.05
                ],
                [
                    35.08,
                    34.62,
                    34.15,
                    36.22
                ],
                [
                    34.31,
                    35.73,
                    34.15,
                    35.82
                ],
                [
                    34.62,
                    36.72,
                    34.37,
                    36.77
                ],
                [
                    37.03,
                    36.08,
                    36.08,
                    37.89
                ],
                [
                    36.2,
                    36.5,
                    35.62,
                    37.15
                ],
                [
                    36.46,
                    37.69,
                    36.0,
                    37.69
                ],
                [
                    37.98,
                    37.28,
                    37.23,
                    38.69
                ],
                [
                    38.08,
                    37.27,
                    36.78,
                    38.23
                ],
                [
                    36.9,
                    34.62,
                    34.48,
                    36.9
                ],
                [
                    34.5,
                    34.22,
                    34.08,
                    35.09
                ],
                [
                    34.18,
                    33.81,
                    33.35,
                    34.38
                ],
                [
                    33.76,
                    33.37,
                    32.68,
                    34.15
                ],
                [
                    33.48,
                    31.48,
                    30.97,
                    33.78
                ],
                [
                    31.07,
                    31.16,
                    30.92,
                    31.46
                ],
                [
                    31.07,
                    30.45,
                    30.31,
                    31.37
                ],
                [
                    30.36,
                    29.87,
                    29.79,
                    30.62
                ],
                [
                    29.66,
                    29.79,
                    28.94,
                    30.32
                ],
                [
                    30.02,
                    29.71,
                    29.69,
                    30.68
                ],
                [
                    29.7,
                    29.69,
                    29.61,
                    30.06
                ],
                [
                    29.01,
                    28.62,
                    28.46,
                    29.75
                ],
                [
                    25.75,
                    25.75,
                    25.75,
                    26.38
                ],
                [
                    24.48,
                    23.15,
                    23.15,
                    25.14
                ],
                [
                    22.58,
                    24.25,
                    21.51,
                    24.58
                ],
                [
                    25.67,
                    25.14,
                    25.12,
                    26.25
                ],
                [
                    24.69,
                    25.6,
                    24.65,
                    25.88
                ],
                [
                    26.47,
                    25.76,
                    25.68,
                    26.48
                ],
                [
                    25.81,
                    25.77,
                    25.51,
                    26.18
                ],
                [
                    25.51,
                    24.75,
                    24.42,
                    25.67
                ],
                [
                    24.51,
                    24.85,
                    24.51,
                    25.43
                ],
                [
                    25.05,
                    25.51,
                    24.71,
                    25.83
                ],
                [
                    25.72,
                    26.35,
                    25.15,
                    26.42
                ],
                [
                    24.85,
                    23.7,
                    23.7,
                    24.85
                ],
                [
                    23.98,
                    23.98,
                    23.14,
                    24.3
                ],
                [
                    24.21,
                    23.22,
                    23.18,
                    24.33
                ],
                [
                    23.46,
                    23.22,
                    22.97,
                    23.93
                ],
                [
                    23.08,
                    23.06,
                    22.94,
                    23.78
                ],
                [
                    23.0,
                    23.64,
                    22.69,
                    23.98
                ],
                [
                    23.72,
                    24.16,
                    23.46,
                    24.3
                ],
                [
                    25.06,
                    25.73,
                    24.69,
                    25.99
                ],
                [
                    26.37,
                    25.91,
                    25.65,
                    26.61
                ],
                [
                    26.0,
                    26.45,
                    25.85,
                    26.65
                ],
                [
                    26.44,
                    25.59,
                    25.46,
                    26.44
                ],
                [
                    26.07,
                    26.41,
                    25.91,
                    26.69
                ],
                [
                    26.97,
                    26.05,
                    25.98,
                    27.02
                ],
                [
                    26.38,
                    26.35,
                    26.18,
                    27.22
                ],
                [
                    26.17,
                    25.33,
                    25.18,
                    26.18
                ],
                [
                    25.16,
                    25.05,
                    25.02,
                    25.6
                ],
                [
                    25.08,
                    25.05,
                    24.58,
                    25.12
                ],
                [
                    24.92,
                    25.02,
                    24.58,
                    25.2
                ],
                [
                    24.92,
                    24.64,
                    24.58,
                    24.96
                ],
                [
                    24.62,
                    24.28,
                    24.25,
                    24.81
                ],
                [
                    24.31,
                    24.08,
                    24.08,
                    24.63
                ],
                [
                    24.11,
                    24.63,
                    24.11,
                    24.77
                ],
                [
                    24.62,
                    24.09,
                    24.06,
                    24.63
                ],
                [
                    24.09,
                    24.09,
                    23.87,
                    24.46
                ],
                [
                    24.32,
                    24.77,
                    24.25,
                    24.92
                ],
                [
                    24.69,
                    24.26,
                    23.88,
                    24.69
                ],
                [
                    23.78,
                    24.05,
                    23.6,
                    24.19
                ],
                [
                    24.16,
                    25.38,
                    24.12,
                    25.5
                ],
                [
                    25.52,
                    26.88,
                    25.13,
                    26.97
                ],
                [
                    26.89,
                    26.72,
                    26.37,
                    27.59
                ],
                [
                    26.9,
                    27.17,
                    26.76,
                    27.79
                ],
                [
                    27.27,
                    26.75,
                    26.18,
                    27.28
                ],
                [
                    26.77,
                    26.51,
                    26.44,
                    27.11
                ],
                [
                    26.41,
                    27.17,
                    26.12,
                    28.0
                ],
                [
                    27.0,
                    27.08,
                    26.64,
                    27.53
                ],
                [
                    26.79,
                    28.78,
                    26.68,
                    29.12
                ],
                [
                    28.99,
                    28.7,
                    28.52,
                    29.35
                ],
                [
                    28.71,
                    29.88,
                    28.17,
                    30.39
                ],
                [
                    29.88,
                    30.24,
                    29.45,
                    30.66
                ],
                [
                    29.8,
                    28.57,
                    28.33,
                    30.1
                ],
                [
                    28.38,
                    27.25,
                    27.1,
                    29.0
                ],
                [
                    27.27,
                    27.72,
                    27.26,
                    27.85
                ],
                [
                    28.1,
                    29.07,
                    28.05,
                    29.49
                ],
                [
                    29.28,
                    28.86,
                    28.73,
                    29.55
                ],
                [
                    28.8,
                    29.69,
                    28.36,
                    29.88
                ],
                [
                    29.99,
                    29.71,
                    29.6,
                    30.56
                ],
                [
                    29.73,
                    29.46,
                    29.4,
                    30.14
                ],
                [
                    29.11,
                    28.74,
                    28.64,
                    29.29
                ],
                [
                    28.89,
                    28.84,
                    28.66,
                    29.08
                ],
                [
                    29.25,
                    29.41,
                    29.09,
                    30.14
                ],
                [
                    28.8,
                    29.01,
                    28.48,
                    29.81
                ],
                [
                    28.83,
                    30.86,
                    28.54,
                    30.87
                ],
                [
                    30.87,
                    30.59,
                    30.23,
                    30.98
                ],
                [
                    30.6,
                    30.99,
                    29.9,
                    31.25
                ],
                [
                    30.67,
                    30.63,
                    30.3,
                    30.95
                ],
                [
                    30.66,
                    31.84,
                    30.63,
                    32.37
                ]
            ],
            "itemStyle": {
                "color": "#ef232a",
                "color0": "#14b143",
                "borderColor": "#ef232a",
                "borderColor0": "#14b143"
            },
            "selectedMode": false,
            "large": false,
            "clip": true,
            "zlevel": 0,
            "z": 2
        },
        {
            "type": "line",
            "name": "MA5",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    29.68
                ],
                [
                    "2025-01-20",
                    30.17
                ],
                [
                    "2025-01-21",
                    30.44
                ],
                [
                    "2025-01-22",
                    31.33
                ],
                [
                    "2025-01-23",
                    32.24
                ],
                [
                    "2025-01-24",
                    33.27
                ],
                [
                    "2025-01-27",
                    33.3
                ],
                [
                    "2025-02-05",
                    32.6
                ],
                [
                    "2025-02-06",
                    31.65
                ],
                [
                    "2025-02-07",
                    31.08
                ],
                [
                    "2025-02-10",
                    30.43
                ],
                [
                    "2025-02-11",
                    30.48
                ],
                [
                    "2025-02-12",
                    31.27
                ],
                [
                    "2025-02-13",
                    31.53
                ],
                [
                    "2025-02-14",
                    31.42
                ],
                [
                    "2025-02-17",
                    31.85
                ],
                [
                    "2025-02-18",
                    32.1
                ],
                [
                    "2025-02-19",
                    32.32
                ],
                [
                    "2025-02-20",
                    32.8
                ],
                [
                    "2025-02-21",
                    33.98
                ],
                [
                    "2025-02-24",
                    34.99
                ],
                [
                    "2025-02-25",
                    35.81
                ],
                [
                    "2025-02-26",
                    36.5
                ],
                [
                    "2025-02-27",
                    37.01
                ],
                [
                    "2025-02-28",
                    36.61
                ],
                [
                    "2025-03-03",
                    35.5
                ],
                [
                    "2025-03-04",
                    34.81
                ],
                [
                    "2025-03-05",
                    34.3
                ],
                [
                    "2025-03-06",
                    34.26
                ],
                [
                    "2025-03-07",
                    34.27
                ],
                [
                    "2025-03-10",
                    34.75
                ],
                [
                    "2025-03-11",
                    35.41
                ],
                [
                    "2025-03-12",
                    35.75
                ],
                [
                    "2025-03-13",
                    35.93
                ],
                [
                    "2025-03-14",
                    36.54
                ],
                [
                    "2025-03-17",
                    36.85
                ],
                [
                    "2025-03-18",
                    36.96
                ],
                [
                    "2025-03-19",
                    36.67
                ],
                [
                    "2025-03-20",
                    36.22
                ],
                [
                    "2025-03-21",
                    35.44
                ],
                [
                    "2025-03-24",
                    34.66
                ],
                [
                    "2025-03-25",
                    33.5
                ],
                [
                    "2025-03-26",
                    32.81
                ],
                [
                    "2025-03-27",
                    32.05
                ],
                [
                    "2025-03-28",
                    31.27
                ],
                [
                    "2025-03-31",
                    30.55
                ],
                [
                    "2025-04-01",
                    30.2
                ],
                [
                    "2025-04-02",
                    29.9
                ],
                [
                    "2025-04-03",
                    29.54
                ],
                [
                    "2025-04-07",
                    28.71
                ],
                [
                    "2025-04-08",
                    27.38
                ],
                [
                    "2025-04-09",
                    26.29
                ],
                [
                    "2025-04-10",
                    25.38
                ],
                [
                    "2025-04-11",
                    24.78
                ],
                [
                    "2025-04-14",
                    24.78
                ],
                [
                    "2025-04-15",
                    25.3
                ],
                [
                    "2025-04-16",
                    25.4
                ],
                [
                    "2025-04-17",
                    25.35
                ],
                [
                    "2025-04-18",
                    25.33
                ],
                [
                    "2025-04-21",
                    25.45
                ],
                [
                    "2025-04-22",
                    25.03
                ],
                [
                    "2025-04-23",
                    24.88
                ],
                [
                    "2025-04-24",
                    24.55
                ],
                [
                    "2025-04-25",
                    24.09
                ],
                [
                    "2025-04-28",
                    23.44
                ],
                [
                    "2025-04-29",
                    23.42
                ],
                [
                    "2025-04-30",
                    23.46
                ],
                [
                    "2025-05-06",
                    23.96
                ],
                [
                    "2025-05-07",
                    24.5
                ],
                [
                    "2025-05-08",
                    25.18
                ],
                [
                    "2025-05-09",
                    25.57
                ],
                [
                    "2025-05-12",
                    26.02
                ],
                [
                    "2025-05-13",
                    26.08
                ],
                [
                    "2025-05-14",
                    26.17
                ],
                [
                    "2025-05-15",
                    25.95
                ],
                [
                    "2025-05-16",
                    25.84
                ],
                [
                    "2025-05-19",
                    25.57
                ],
                [
                    "2025-05-20",
                    25.36
                ],
                [
                    "2025-05-21",
                    25.02
                ],
                [
                    "2025-05-22",
                    24.81
                ],
                [
                    "2025-05-23",
                    24.61
                ],
                [
                    "2025-05-26",
                    24.53
                ],
                [
                    "2025-05-27",
                    24.34
                ],
                [
                    "2025-05-28",
                    24.23
                ],
                [
                    "2025-05-29",
                    24.33
                ],
                [
                    "2025-05-30",
                    24.37
                ],
                [
                    "2025-06-03",
                    24.25
                ],
                [
                    "2025-06-04",
                    24.51
                ],
                [
                    "2025-06-05",
                    25.07
                ],
                [
                    "2025-06-06",
                    25.46
                ],
                [
                    "2025-06-09",
                    26.04
                ],
                [
                    "2025-06-10",
                    26.58
                ],
                [
                    "2025-06-11",
                    26.81
                ],
                [
                    "2025-06-12",
                    26.86
                ],
                [
                    "2025-06-13",
                    26.94
                ],
                [
                    "2025-06-16",
                    27.26
                ],
                [
                    "2025-06-17",
                    27.65
                ],
                [
                    "2025-06-18",
                    28.32
                ],
                [
                    "2025-06-19",
                    28.94
                ],
                [
                    "2025-06-20",
                    29.23
                ],
                [
                    "2025-06-23",
                    28.93
                ],
                [
                    "2025-06-24",
                    28.73
                ],
                [
                    "2025-06-25",
                    28.57
                ],
                [
                    "2025-06-26",
                    28.29
                ],
                [
                    "2025-06-27",
                    28.52
                ],
                [
                    "2025-06-30",
                    29.01
                ],
                [
                    "2025-07-01",
                    29.36
                ],
                [
                    "2025-07-02",
                    29.29
                ],
                [
                    "2025-07-03",
                    29.29
                ],
                [
                    "2025-07-04",
                    29.23
                ],
                [
                    "2025-07-07",
                    29.09
                ],
                [
                    "2025-07-08",
                    29.37
                ],
                [
                    "2025-07-09",
                    29.74
                ],
                [
                    "2025-07-10",
                    30.17
                ],
                [
                    "2025-07-11",
                    30.42
                ],
                [
                    "2025-07-14",
                    30.98
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.8,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "MA10",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    31.48
                ],
                [
                    "2025-01-27",
                    31.74
                ],
                [
                    "2025-02-05",
                    31.52
                ],
                [
                    "2025-02-06",
                    31.49
                ],
                [
                    "2025-02-07",
                    31.66
                ],
                [
                    "2025-02-10",
                    31.85
                ],
                [
                    "2025-02-11",
                    31.89
                ],
                [
                    "2025-02-12",
                    31.93
                ],
                [
                    "2025-02-13",
                    31.59
                ],
                [
                    "2025-02-14",
                    31.25
                ],
                [
                    "2025-02-17",
                    31.14
                ],
                [
                    "2025-02-18",
                    31.29
                ],
                [
                    "2025-02-19",
                    31.79
                ],
                [
                    "2025-02-20",
                    32.17
                ],
                [
                    "2025-02-21",
                    32.7
                ],
                [
                    "2025-02-24",
                    33.42
                ],
                [
                    "2025-02-25",
                    33.95
                ],
                [
                    "2025-02-26",
                    34.41
                ],
                [
                    "2025-02-27",
                    34.91
                ],
                [
                    "2025-02-28",
                    35.29
                ],
                [
                    "2025-03-03",
                    35.25
                ],
                [
                    "2025-03-04",
                    35.31
                ],
                [
                    "2025-03-05",
                    35.4
                ],
                [
                    "2025-03-06",
                    35.64
                ],
                [
                    "2025-03-07",
                    35.44
                ],
                [
                    "2025-03-10",
                    35.13
                ],
                [
                    "2025-03-11",
                    35.11
                ],
                [
                    "2025-03-12",
                    35.02
                ],
                [
                    "2025-03-13",
                    35.09
                ],
                [
                    "2025-03-14",
                    35.41
                ],
                [
                    "2025-03-17",
                    35.8
                ],
                [
                    "2025-03-18",
                    36.19
                ],
                [
                    "2025-03-19",
                    36.21
                ],
                [
                    "2025-03-20",
                    36.07
                ],
                [
                    "2025-03-21",
                    35.99
                ],
                [
                    "2025-03-24",
                    35.76
                ],
                [
                    "2025-03-25",
                    35.23
                ],
                [
                    "2025-03-26",
                    34.74
                ],
                [
                    "2025-03-27",
                    34.14
                ],
                [
                    "2025-03-28",
                    33.35
                ],
                [
                    "2025-03-31",
                    32.6
                ],
                [
                    "2025-04-01",
                    31.85
                ],
                [
                    "2025-04-02",
                    31.35
                ],
                [
                    "2025-04-03",
                    30.8
                ],
                [
                    "2025-04-07",
                    29.99
                ],
                [
                    "2025-04-08",
                    28.97
                ],
                [
                    "2025-04-09",
                    28.24
                ],
                [
                    "2025-04-10",
                    27.64
                ],
                [
                    "2025-04-11",
                    27.16
                ],
                [
                    "2025-04-14",
                    26.75
                ],
                [
                    "2025-04-15",
                    26.34
                ],
                [
                    "2025-04-16",
                    25.85
                ],
                [
                    "2025-04-17",
                    25.36
                ],
                [
                    "2025-04-18",
                    25.05
                ],
                [
                    "2025-04-21",
                    25.11
                ],
                [
                    "2025-04-22",
                    25.17
                ],
                [
                    "2025-04-23",
                    25.14
                ],
                [
                    "2025-04-24",
                    24.95
                ],
                [
                    "2025-04-25",
                    24.71
                ],
                [
                    "2025-04-28",
                    24.44
                ],
                [
                    "2025-04-29",
                    24.23
                ],
                [
                    "2025-04-30",
                    24.17
                ],
                [
                    "2025-05-06",
                    24.26
                ],
                [
                    "2025-05-07",
                    24.3
                ],
                [
                    "2025-05-08",
                    24.31
                ],
                [
                    "2025-05-09",
                    24.5
                ],
                [
                    "2025-05-12",
                    24.74
                ],
                [
                    "2025-05-13",
                    25.02
                ],
                [
                    "2025-05-14",
                    25.34
                ],
                [
                    "2025-05-15",
                    25.56
                ],
                [
                    "2025-05-16",
                    25.7
                ],
                [
                    "2025-05-19",
                    25.79
                ],
                [
                    "2025-05-20",
                    25.72
                ],
                [
                    "2025-05-21",
                    25.59
                ],
                [
                    "2025-05-22",
                    25.38
                ],
                [
                    "2025-05-23",
                    25.23
                ],
                [
                    "2025-05-26",
                    25.05
                ],
                [
                    "2025-05-27",
                    24.85
                ],
                [
                    "2025-05-28",
                    24.63
                ],
                [
                    "2025-05-29",
                    24.57
                ],
                [
                    "2025-05-30",
                    24.49
                ],
                [
                    "2025-06-03",
                    24.39
                ],
                [
                    "2025-06-04",
                    24.43
                ],
                [
                    "2025-06-05",
                    24.65
                ],
                [
                    "2025-06-06",
                    24.9
                ],
                [
                    "2025-06-09",
                    25.2
                ],
                [
                    "2025-06-10",
                    25.42
                ],
                [
                    "2025-06-11",
                    25.66
                ],
                [
                    "2025-06-12",
                    25.97
                ],
                [
                    "2025-06-13",
                    26.2
                ],
                [
                    "2025-06-16",
                    26.65
                ],
                [
                    "2025-06-17",
                    27.11
                ],
                [
                    "2025-06-18",
                    27.56
                ],
                [
                    "2025-06-19",
                    27.9
                ],
                [
                    "2025-06-20",
                    28.08
                ],
                [
                    "2025-06-23",
                    28.09
                ],
                [
                    "2025-06-24",
                    28.19
                ],
                [
                    "2025-06-25",
                    28.45
                ],
                [
                    "2025-06-26",
                    28.62
                ],
                [
                    "2025-06-27",
                    28.88
                ],
                [
                    "2025-06-30",
                    28.97
                ],
                [
                    "2025-07-01",
                    29.04
                ],
                [
                    "2025-07-02",
                    28.93
                ],
                [
                    "2025-07-03",
                    28.79
                ],
                [
                    "2025-07-04",
                    28.88
                ],
                [
                    "2025-07-07",
                    29.05
                ],
                [
                    "2025-07-08",
                    29.36
                ],
                [
                    "2025-07-09",
                    29.52
                ],
                [
                    "2025-07-10",
                    29.73
                ],
                [
                    "2025-07-11",
                    29.82
                ],
                [
                    "2025-07-14",
                    30.04
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.8,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "MA20",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    null
                ],
                [
                    "2025-01-27",
                    null
                ],
                [
                    "2025-02-05",
                    null
                ],
                [
                    "2025-02-06",
                    null
                ],
                [
                    "2025-02-07",
                    null
                ],
                [
                    "2025-02-10",
                    null
                ],
                [
                    "2025-02-11",
                    null
                ],
                [
                    "2025-02-12",
                    null
                ],
                [
                    "2025-02-13",
                    null
                ],
                [
                    "2025-02-14",
                    null
                ],
                [
                    "2025-02-17",
                    31.31
                ],
                [
                    "2025-02-18",
                    31.51
                ],
                [
                    "2025-02-19",
                    31.66
                ],
                [
                    "2025-02-20",
                    31.83
                ],
                [
                    "2025-02-21",
                    32.18
                ],
                [
                    "2025-02-24",
                    32.64
                ],
                [
                    "2025-02-25",
                    32.92
                ],
                [
                    "2025-02-26",
                    33.17
                ],
                [
                    "2025-02-27",
                    33.25
                ],
                [
                    "2025-02-28",
                    33.27
                ],
                [
                    "2025-03-03",
                    33.19
                ],
                [
                    "2025-03-04",
                    33.3
                ],
                [
                    "2025-03-05",
                    33.6
                ],
                [
                    "2025-03-06",
                    33.9
                ],
                [
                    "2025-03-07",
                    34.07
                ],
                [
                    "2025-03-10",
                    34.28
                ],
                [
                    "2025-03-11",
                    34.53
                ],
                [
                    "2025-03-12",
                    34.72
                ],
                [
                    "2025-03-13",
                    35.0
                ],
                [
                    "2025-03-14",
                    35.35
                ],
                [
                    "2025-03-17",
                    35.52
                ],
                [
                    "2025-03-18",
                    35.75
                ],
                [
                    "2025-03-19",
                    35.8
                ],
                [
                    "2025-03-20",
                    35.85
                ],
                [
                    "2025-03-21",
                    35.72
                ],
                [
                    "2025-03-24",
                    35.44
                ],
                [
                    "2025-03-25",
                    35.17
                ],
                [
                    "2025-03-26",
                    34.88
                ],
                [
                    "2025-03-27",
                    34.61
                ],
                [
                    "2025-03-28",
                    34.38
                ],
                [
                    "2025-03-31",
                    34.2
                ],
                [
                    "2025-04-01",
                    34.02
                ],
                [
                    "2025-04-02",
                    33.78
                ],
                [
                    "2025-04-03",
                    33.43
                ],
                [
                    "2025-04-07",
                    32.99
                ],
                [
                    "2025-04-08",
                    32.36
                ],
                [
                    "2025-04-09",
                    31.74
                ],
                [
                    "2025-04-10",
                    31.19
                ],
                [
                    "2025-04-11",
                    30.65
                ],
                [
                    "2025-04-14",
                    30.05
                ],
                [
                    "2025-04-15",
                    29.47
                ],
                [
                    "2025-04-16",
                    28.85
                ],
                [
                    "2025-04-17",
                    28.36
                ],
                [
                    "2025-04-18",
                    27.92
                ],
                [
                    "2025-04-21",
                    27.55
                ],
                [
                    "2025-04-22",
                    27.07
                ],
                [
                    "2025-04-23",
                    26.69
                ],
                [
                    "2025-04-24",
                    26.3
                ],
                [
                    "2025-04-25",
                    25.93
                ],
                [
                    "2025-04-28",
                    25.59
                ],
                [
                    "2025-04-29",
                    25.29
                ],
                [
                    "2025-04-30",
                    25.01
                ],
                [
                    "2025-05-06",
                    24.81
                ],
                [
                    "2025-05-07",
                    24.68
                ],
                [
                    "2025-05-08",
                    24.71
                ],
                [
                    "2025-05-09",
                    24.83
                ],
                [
                    "2025-05-12",
                    24.94
                ],
                [
                    "2025-05-13",
                    24.99
                ],
                [
                    "2025-05-14",
                    25.02
                ],
                [
                    "2025-05-15",
                    25.0
                ],
                [
                    "2025-05-16",
                    24.97
                ],
                [
                    "2025-05-19",
                    24.98
                ],
                [
                    "2025-05-20",
                    24.99
                ],
                [
                    "2025-05-21",
                    24.95
                ],
                [
                    "2025-05-22",
                    24.84
                ],
                [
                    "2025-05-23",
                    24.86
                ],
                [
                    "2025-05-26",
                    24.89
                ],
                [
                    "2025-05-27",
                    24.94
                ],
                [
                    "2025-05-28",
                    24.98
                ],
                [
                    "2025-05-29",
                    25.07
                ],
                [
                    "2025-05-30",
                    25.1
                ],
                [
                    "2025-06-03",
                    25.09
                ],
                [
                    "2025-06-04",
                    25.07
                ],
                [
                    "2025-06-05",
                    25.12
                ],
                [
                    "2025-06-06",
                    25.14
                ],
                [
                    "2025-06-09",
                    25.22
                ],
                [
                    "2025-06-10",
                    25.23
                ],
                [
                    "2025-06-11",
                    25.26
                ],
                [
                    "2025-06-12",
                    25.3
                ],
                [
                    "2025-06-13",
                    25.38
                ],
                [
                    "2025-06-16",
                    25.57
                ],
                [
                    "2025-06-17",
                    25.75
                ],
                [
                    "2025-06-18",
                    26.0
                ],
                [
                    "2025-06-19",
                    26.28
                ],
                [
                    "2025-06-20",
                    26.49
                ],
                [
                    "2025-06-23",
                    26.65
                ],
                [
                    "2025-06-24",
                    26.8
                ],
                [
                    "2025-06-25",
                    27.05
                ],
                [
                    "2025-06-26",
                    27.29
                ],
                [
                    "2025-06-27",
                    27.54
                ],
                [
                    "2025-06-30",
                    27.81
                ],
                [
                    "2025-07-01",
                    28.08
                ],
                [
                    "2025-07-02",
                    28.25
                ],
                [
                    "2025-07-03",
                    28.35
                ],
                [
                    "2025-07-04",
                    28.48
                ],
                [
                    "2025-07-07",
                    28.57
                ],
                [
                    "2025-07-08",
                    28.78
                ],
                [
                    "2025-07-09",
                    28.98
                ],
                [
                    "2025-07-10",
                    29.17
                ],
                [
                    "2025-07-11",
                    29.35
                ],
                [
                    "2025-07-14",
                    29.5
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.8,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "MA30",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": true,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-13",
                    null
                ],
                [
                    "2025-01-14",
                    null
                ],
                [
                    "2025-01-15",
                    null
                ],
                [
                    "2025-01-16",
                    null
                ],
                [
                    "2025-01-17",
                    null
                ],
                [
                    "2025-01-20",
                    null
                ],
                [
                    "2025-01-21",
                    null
                ],
                [
                    "2025-01-22",
                    null
                ],
                [
                    "2025-01-23",
                    null
                ],
                [
                    "2025-01-24",
                    null
                ],
                [
                    "2025-01-27",
                    null
                ],
                [
                    "2025-02-05",
                    null
                ],
                [
                    "2025-02-06",
                    null
                ],
                [
                    "2025-02-07",
                    null
                ],
                [
                    "2025-02-10",
                    null
                ],
                [
                    "2025-02-11",
                    null
                ],
                [
                    "2025-02-12",
                    null
                ],
                [
                    "2025-02-13",
                    null
                ],
                [
                    "2025-02-14",
                    null
                ],
                [
                    "2025-02-17",
                    null
                ],
                [
                    "2025-02-18",
                    null
                ],
                [
                    "2025-02-19",
                    null
                ],
                [
                    "2025-02-20",
                    null
                ],
                [
                    "2025-02-21",
                    null
                ],
                [
                    "2025-02-24",
                    null
                ],
                [
                    "2025-02-25",
                    null
                ],
                [
                    "2025-02-26",
                    null
                ],
                [
                    "2025-02-27",
                    null
                ],
                [
                    "2025-02-28",
                    null
                ],
                [
                    "2025-03-03",
                    32.62
                ],
                [
                    "2025-03-04",
                    32.78
                ],
                [
                    "2025-03-05",
                    32.9
                ],
                [
                    "2025-03-06",
                    33.1
                ],
                [
                    "2025-03-07",
                    33.27
                ],
                [
                    "2025-03-10",
                    33.47
                ],
                [
                    "2025-03-11",
                    33.65
                ],
                [
                    "2025-03-12",
                    33.79
                ],
                [
                    "2025-03-13",
                    33.86
                ],
                [
                    "2025-03-14",
                    33.98
                ],
                [
                    "2025-03-17",
                    34.06
                ],
                [
                    "2025-03-18",
                    34.26
                ],
                [
                    "2025-03-19",
                    34.47
                ],
                [
                    "2025-03-20",
                    34.62
                ],
                [
                    "2025-03-21",
                    34.71
                ],
                [
                    "2025-03-24",
                    34.77
                ],
                [
                    "2025-03-25",
                    34.76
                ],
                [
                    "2025-03-26",
                    34.72
                ],
                [
                    "2025-03-27",
                    34.71
                ],
                [
                    "2025-03-28",
                    34.68
                ],
                [
                    "2025-03-31",
                    34.55
                ],
                [
                    "2025-04-01",
                    34.45
                ],
                [
                    "2025-04-02",
                    34.32
                ],
                [
                    "2025-04-03",
                    34.17
                ],
                [
                    "2025-04-07",
                    33.81
                ],
                [
                    "2025-04-08",
                    33.28
                ],
                [
                    "2025-04-09",
                    32.86
                ],
                [
                    "2025-04-10",
                    32.47
                ],
                [
                    "2025-04-11",
                    32.13
                ],
                [
                    "2025-04-14",
                    31.84
                ],
                [
                    "2025-04-15",
                    31.58
                ],
                [
                    "2025-04-16",
                    31.3
                ],
                [
                    "2025-04-17",
                    30.98
                ],
                [
                    "2025-04-18",
                    30.64
                ],
                [
                    "2025-04-21",
                    30.36
                ],
                [
                    "2025-04-22",
                    29.96
                ],
                [
                    "2025-04-23",
                    29.54
                ],
                [
                    "2025-04-24",
                    29.11
                ],
                [
                    "2025-04-25",
                    28.67
                ],
                [
                    "2025-04-28",
                    28.18
                ],
                [
                    "2025-04-29",
                    27.73
                ],
                [
                    "2025-04-30",
                    27.29
                ],
                [
                    "2025-05-06",
                    26.99
                ],
                [
                    "2025-05-07",
                    26.71
                ],
                [
                    "2025-05-08",
                    26.47
                ],
                [
                    "2025-05-09",
                    26.21
                ],
                [
                    "2025-05-12",
                    26.04
                ],
                [
                    "2025-05-13",
                    25.87
                ],
                [
                    "2025-05-14",
                    25.73
                ],
                [
                    "2025-05-15",
                    25.58
                ],
                [
                    "2025-05-16",
                    25.42
                ],
                [
                    "2025-05-19",
                    25.27
                ],
                [
                    "2025-05-20",
                    25.11
                ],
                [
                    "2025-05-21",
                    24.98
                ],
                [
                    "2025-05-22",
                    24.93
                ],
                [
                    "2025-05-23",
                    24.96
                ],
                [
                    "2025-05-26",
                    24.98
                ],
                [
                    "2025-05-27",
                    24.94
                ],
                [
                    "2025-05-28",
                    24.89
                ],
                [
                    "2025-05-29",
                    24.86
                ],
                [
                    "2025-05-30",
                    24.81
                ],
                [
                    "2025-06-03",
                    24.78
                ],
                [
                    "2025-06-04",
                    24.8
                ],
                [
                    "2025-06-05",
                    24.85
                ],
                [
                    "2025-06-06",
                    24.86
                ],
                [
                    "2025-06-09",
                    24.98
                ],
                [
                    "2025-06-10",
                    25.07
                ],
                [
                    "2025-06-11",
                    25.18
                ],
                [
                    "2025-06-12",
                    25.31
                ],
                [
                    "2025-06-13",
                    25.44
                ],
                [
                    "2025-06-16",
                    25.61
                ],
                [
                    "2025-06-17",
                    25.77
                ],
                [
                    "2025-06-18",
                    25.9
                ],
                [
                    "2025-06-19",
                    26.05
                ],
                [
                    "2025-06-20",
                    26.12
                ],
                [
                    "2025-06-23",
                    26.17
                ],
                [
                    "2025-06-24",
                    26.22
                ],
                [
                    "2025-06-25",
                    26.32
                ],
                [
                    "2025-06-26",
                    26.4
                ],
                [
                    "2025-06-27",
                    26.55
                ],
                [
                    "2025-06-30",
                    26.7
                ],
                [
                    "2025-07-01",
                    26.85
                ],
                [
                    "2025-07-02",
                    26.97
                ],
                [
                    "2025-07-03",
                    27.11
                ],
                [
                    "2025-07-04",
                    27.28
                ],
                [
                    "2025-07-07",
                    27.45
                ],
                [
                    "2025-07-08",
                    27.66
                ],
                [
                    "2025-07-09",
                    27.87
                ],
                [
                    "2025-07-10",
                    28.1
                ],
                [
                    "2025-07-11",
                    28.3
                ],
                [
                    "2025-07-14",
                    28.55
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 0.8,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u652f\u6491\u7ebf (3\u70b9, \u4e0a\u5347, \u6781\u5f3a)",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-13",
                    18.23
                ],
                [
                    "2025-01-14",
                    18.29
                ],
                [
                    "2025-01-15",
                    18.35
                ],
                [
                    "2025-01-16",
                    18.4
                ],
                [
                    "2025-01-17",
                    18.46
                ],
                [
                    "2025-01-20",
                    18.52
                ],
                [
                    "2025-01-21",
                    18.58
                ],
                [
                    "2025-01-22",
                    18.64
                ],
                [
                    "2025-01-23",
                    18.7
                ],
                [
                    "2025-01-24",
                    18.76
                ],
                [
                    "2025-01-27",
                    18.82
                ],
                [
                    "2025-02-05",
                    18.88
                ],
                [
                    "2025-02-06",
                    18.94
                ],
                [
                    "2025-02-07",
                    19.0
                ],
                [
                    "2025-02-10",
                    19.06
                ],
                [
                    "2025-02-11",
                    19.12
                ],
                [
                    "2025-02-12",
                    19.18
                ],
                [
                    "2025-02-13",
                    19.24
                ],
                [
                    "2025-02-14",
                    19.3
                ],
                [
                    "2025-02-17",
                    19.36
                ],
                [
                    "2025-02-18",
                    19.42
                ],
                [
                    "2025-02-19",
                    19.48
                ],
                [
                    "2025-02-20",
                    19.54
                ],
                [
                    "2025-02-21",
                    19.6
                ],
                [
                    "2025-02-24",
                    19.66
                ],
                [
                    "2025-02-25",
                    19.72
                ],
                [
                    "2025-02-26",
                    19.78
                ],
                [
                    "2025-02-27",
                    19.84
                ],
                [
                    "2025-02-28",
                    19.9
                ],
                [
                    "2025-03-03",
                    19.96
                ],
                [
                    "2025-03-04",
                    20.02
                ],
                [
                    "2025-03-05",
                    20.08
                ],
                [
                    "2025-03-06",
                    20.14
                ],
                [
                    "2025-03-07",
                    20.2
                ],
                [
                    "2025-03-10",
                    20.26
                ],
                [
                    "2025-03-11",
                    20.32
                ],
                [
                    "2025-03-12",
                    20.38
                ],
                [
                    "2025-03-13",
                    20.44
                ],
                [
                    "2025-03-14",
                    20.49
                ],
                [
                    "2025-03-17",
                    20.55
                ],
                [
                    "2025-03-18",
                    20.61
                ],
                [
                    "2025-03-19",
                    20.67
                ],
                [
                    "2025-03-20",
                    20.73
                ],
                [
                    "2025-03-21",
                    20.79
                ],
                [
                    "2025-03-24",
                    20.85
                ],
                [
                    "2025-03-25",
                    20.91
                ],
                [
                    "2025-03-26",
                    20.97
                ],
                [
                    "2025-03-27",
                    21.03
                ],
                [
                    "2025-03-28",
                    21.09
                ],
                [
                    "2025-03-31",
                    21.15
                ],
                [
                    "2025-04-01",
                    21.21
                ],
                [
                    "2025-04-02",
                    21.27
                ],
                [
                    "2025-04-03",
                    21.33
                ],
                [
                    "2025-04-07",
                    21.39
                ],
                [
                    "2025-04-08",
                    21.45
                ],
                [
                    "2025-04-09",
                    21.51
                ],
                [
                    "2025-04-10",
                    21.57
                ],
                [
                    "2025-04-11",
                    21.63
                ],
                [
                    "2025-04-14",
                    21.69
                ],
                [
                    "2025-04-15",
                    21.75
                ],
                [
                    "2025-04-16",
                    21.81
                ],
                [
                    "2025-04-17",
                    21.87
                ],
                [
                    "2025-04-18",
                    21.93
                ],
                [
                    "2025-04-21",
                    21.99
                ],
                [
                    "2025-04-22",
                    22.05
                ],
                [
                    "2025-04-23",
                    22.11
                ],
                [
                    "2025-04-24",
                    22.17
                ],
                [
                    "2025-04-25",
                    22.23
                ],
                [
                    "2025-04-28",
                    22.29
                ],
                [
                    "2025-04-29",
                    22.35
                ],
                [
                    "2025-04-30",
                    22.41
                ],
                [
                    "2025-05-06",
                    22.47
                ],
                [
                    "2025-05-07",
                    22.53
                ],
                [
                    "2025-05-08",
                    22.58
                ],
                [
                    "2025-05-09",
                    22.64
                ],
                [
                    "2025-05-12",
                    22.7
                ],
                [
                    "2025-05-13",
                    22.76
                ],
                [
                    "2025-05-14",
                    22.82
                ],
                [
                    "2025-05-15",
                    22.88
                ],
                [
                    "2025-05-16",
                    22.94
                ],
                [
                    "2025-05-19",
                    23.0
                ],
                [
                    "2025-05-20",
                    23.06
                ],
                [
                    "2025-05-21",
                    23.12
                ],
                [
                    "2025-05-22",
                    23.18
                ],
                [
                    "2025-05-23",
                    23.24
                ],
                [
                    "2025-05-26",
                    23.3
                ],
                [
                    "2025-05-27",
                    23.36
                ],
                [
                    "2025-05-28",
                    23.42
                ],
                [
                    "2025-05-29",
                    23.48
                ],
                [
                    "2025-05-30",
                    23.54
                ],
                [
                    "2025-06-03",
                    23.6
                ],
                [
                    "2025-06-04",
                    23.66
                ],
                [
                    "2025-06-05",
                    23.72
                ],
                [
                    "2025-06-06",
                    23.78
                ],
                [
                    "2025-06-09",
                    23.84
                ],
                [
                    "2025-06-10",
                    23.9
                ],
                [
                    "2025-06-11",
                    23.96
                ],
                [
                    "2025-06-12",
                    24.02
                ],
                [
                    "2025-06-13",
                    24.08
                ],
                [
                    "2025-06-16",
                    24.14
                ],
                [
                    "2025-06-17",
                    24.2
                ],
                [
                    "2025-06-18",
                    24.26
                ],
                [
                    "2025-06-19",
                    24.32
                ],
                [
                    "2025-06-20",
                    24.38
                ],
                [
                    "2025-06-23",
                    24.44
                ],
                [
                    "2025-06-24",
                    24.5
                ],
                [
                    "2025-06-25",
                    24.56
                ],
                [
                    "2025-06-26",
                    24.62
                ],
                [
                    "2025-06-27",
                    24.67
                ],
                [
                    "2025-06-30",
                    24.73
                ],
                [
                    "2025-07-01",
                    24.79
                ],
                [
                    "2025-07-02",
                    24.85
                ],
                [
                    "2025-07-03",
                    24.91
                ],
                [
                    "2025-07-04",
                    24.97
                ],
                [
                    "2025-07-07",
                    25.03
                ],
                [
                    "2025-07-08",
                    25.09
                ],
                [
                    "2025-07-09",
                    25.15
                ],
                [
                    "2025-07-10",
                    25.21
                ],
                [
                    "2025-07-11",
                    25.27
                ],
                [
                    "2025-07-14",
                    25.33
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 3,
                "opacity": 0.9,
                "curveness": 0,
                "type": "solid",
                "color": "#FF3333"
            },
            "areaStyle": {
                "opacity": 0
            },
            "tooltip": {
                "show": true,
                "trigger": "item",
                "triggerOn": "mousemove|click",
                "axisPointer": {
                    "type": "line"
                },
                "showContent": true,
                "alwaysShowContent": false,
                "showDelay": 0,
                "hideDelay": 100,
                "enterable": false,
                "confine": false,
                "appendToBody": false,
                "transitionDuration": 0.4,
                "formatter": "\u652f\u6491\u7ebf: y = 0.0597x + 18.2257 (\u8bc4\u5206: 121)",
                "textStyle": {
                    "fontSize": 14
                },
                "borderWidth": 0,
                "padding": 5,
                "order": "seriesAsc"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u963b\u529b\u7ebf (3\u70b9, \u4e0b\u964d, \u6781\u5f3a)",
            "connectNulls": false,
            "xAxisIndex": 0,
            "yAxisIndex": 0,
            "symbolSize": 4,
            "showSymbol": false,
            "smooth": false,
            "clip": true,
            "step": false,
            "stackStrategy": "samesign",
            "data": [
                [
                    "2025-01-13",
                    42.57
                ],
                [
                    "2025-01-14",
                    42.48
                ],
                [
                    "2025-01-15",
                    42.38
                ],
                [
                    "2025-01-16",
                    42.28
                ],
                [
                    "2025-01-17",
                    42.19
                ],
                [
                    "2025-01-20",
                    42.09
                ],
                [
                    "2025-01-21",
                    41.99
                ],
                [
                    "2025-01-22",
                    41.9
                ],
                [
                    "2025-01-23",
                    41.8
                ],
                [
                    "2025-01-24",
                    41.7
                ],
                [
                    "2025-01-27",
                    41.6
                ],
                [
                    "2025-02-05",
                    41.51
                ],
                [
                    "2025-02-06",
                    41.41
                ],
                [
                    "2025-02-07",
                    41.31
                ],
                [
                    "2025-02-10",
                    41.22
                ],
                [
                    "2025-02-11",
                    41.12
                ],
                [
                    "2025-02-12",
                    41.02
                ],
                [
                    "2025-02-13",
                    40.93
                ],
                [
                    "2025-02-14",
                    40.83
                ],
                [
                    "2025-02-17",
                    40.73
                ],
                [
                    "2025-02-18",
                    40.64
                ],
                [
                    "2025-02-19",
                    40.54
                ],
                [
                    "2025-02-20",
                    40.44
                ],
                [
                    "2025-02-21",
                    40.35
                ],
                [
                    "2025-02-24",
                    40.25
                ],
                [
                    "2025-02-25",
                    40.15
                ],
                [
                    "2025-02-26",
                    40.06
                ],
                [
                    "2025-02-27",
                    39.96
                ],
                [
                    "2025-02-28",
                    39.86
                ],
                [
                    "2025-03-03",
                    39.77
                ],
                [
                    "2025-03-04",
                    39.67
                ],
                [
                    "2025-03-05",
                    39.57
                ],
                [
                    "2025-03-06",
                    39.48
                ],
                [
                    "2025-03-07",
                    39.38
                ],
                [
                    "2025-03-10",
                    39.28
                ],
                [
                    "2025-03-11",
                    39.19
                ],
                [
                    "2025-03-12",
                    39.09
                ],
                [
                    "2025-03-13",
                    38.99
                ],
                [
                    "2025-03-14",
                    38.9
                ],
                [
                    "2025-03-17",
                    38.8
                ],
                [
                    "2025-03-18",
                    38.7
                ],
                [
                    "2025-03-19",
                    38.6
                ],
                [
                    "2025-03-20",
                    38.51
                ],
                [
                    "2025-03-21",
                    38.41
                ],
                [
                    "2025-03-24",
                    38.31
                ],
                [
                    "2025-03-25",
                    38.22
                ],
                [
                    "2025-03-26",
                    38.12
                ],
                [
                    "2025-03-27",
                    38.02
                ],
                [
                    "2025-03-28",
                    37.93
                ],
                [
                    "2025-03-31",
                    37.83
                ],
                [
                    "2025-04-01",
                    37.73
                ],
                [
                    "2025-04-02",
                    37.64
                ],
                [
                    "2025-04-03",
                    37.54
                ],
                [
                    "2025-04-07",
                    37.44
                ],
                [
                    "2025-04-08",
                    37.35
                ],
                [
                    "2025-04-09",
                    37.25
                ],
                [
                    "2025-04-10",
                    37.15
                ],
                [
                    "2025-04-11",
                    37.06
                ],
                [
                    "2025-04-14",
                    36.96
                ],
                [
                    "2025-04-15",
                    36.86
                ],
                [
                    "2025-04-16",
                    36.77
                ],
                [
                    "2025-04-17",
                    36.67
                ],
                [
                    "2025-04-18",
                    36.57
                ],
                [
                    "2025-04-21",
                    36.48
                ],
                [
                    "2025-04-22",
                    36.38
                ],
                [
                    "2025-04-23",
                    36.28
                ],
                [
                    "2025-04-24",
                    36.19
                ],
                [
                    "2025-04-25",
                    36.09
                ],
                [
                    "2025-04-28",
                    35.99
                ],
                [
                    "2025-04-29",
                    35.9
                ],
                [
                    "2025-04-30",
                    35.8
                ],
                [
                    "2025-05-06",
                    35.7
                ],
                [
                    "2025-05-07",
                    35.6
                ],
                [
                    "2025-05-08",
                    35.51
                ],
                [
                    "2025-05-09",
                    35.41
                ],
                [
                    "2025-05-12",
                    35.31
                ],
                [
                    "2025-05-13",
                    35.22
                ],
                [
                    "2025-05-14",
                    35.12
                ],
                [
                    "2025-05-15",
                    35.02
                ],
                [
                    "2025-05-16",
                    34.93
                ],
                [
                    "2025-05-19",
                    34.83
                ],
                [
                    "2025-05-20",
                    34.73
                ],
                [
                    "2025-05-21",
                    34.64
                ],
                [
                    "2025-05-22",
                    34.54
                ],
                [
                    "2025-05-23",
                    34.44
                ],
                [
                    "2025-05-26",
                    34.35
                ],
                [
                    "2025-05-27",
                    34.25
                ],
                [
                    "2025-05-28",
                    34.15
                ],
                [
                    "2025-05-29",
                    34.06
                ],
                [
                    "2025-05-30",
                    33.96
                ],
                [
                    "2025-06-03",
                    33.86
                ],
                [
                    "2025-06-04",
                    33.77
                ],
                [
                    "2025-06-05",
                    33.67
                ],
                [
                    "2025-06-06",
                    33.57
                ],
                [
                    "2025-06-09",
                    33.48
                ],
                [
                    "2025-06-10",
                    33.38
                ],
                [
                    "2025-06-11",
                    33.28
                ],
                [
                    "2025-06-12",
                    33.19
                ],
                [
                    "2025-06-13",
                    33.09
                ],
                [
                    "2025-06-16",
                    32.99
                ],
                [
                    "2025-06-17",
                    32.9
                ],
                [
                    "2025-06-18",
                    32.8
                ],
                [
                    "2025-06-19",
                    32.7
                ],
                [
                    "2025-06-20",
                    32.6
                ],
                [
                    "2025-06-23",
                    32.51
                ],
                [
                    "2025-06-24",
                    32.41
                ],
                [
                    "2025-06-25",
                    32.31
                ],
                [
                    "2025-06-26",
                    32.22
                ],
                [
                    "2025-06-27",
                    32.12
                ],
                [
                    "2025-06-30",
                    32.02
                ],
                [
                    "2025-07-01",
                    31.93
                ],
                [
                    "2025-07-02",
                    31.83
                ],
                [
                    "2025-07-03",
                    31.73
                ],
                [
                    "2025-07-04",
                    31.64
                ],
                [
                    "2025-07-07",
                    31.54
                ],
                [
                    "2025-07-08",
                    31.44
                ],
                [
                    "2025-07-09",
                    31.35
                ],
                [
                    "2025-07-10",
                    31.25
                ],
                [
                    "2025-07-11",
                    31.15
                ],
                [
                    "2025-07-14",
                    31.06
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "margin": 8,
                "valueAnimation": false
            },
            "logBase": 10,
            "seriesLayoutBy": "column",
            "lineStyle": {
                "show": true,
                "width": 3,
                "opacity": 0.9,
                "curveness": 0,
                "type": "solid",
                "color": "#4169E1"
            },
            "areaStyle": {
                "opacity": 0
            },
            "tooltip": {
                "show": true,
                "trigger": "item",
                "triggerOn": "mousemove|click",
                "axisPointer": {
                    "type": "line"
                },
                "showContent": true,
                "alwaysShowContent": false,
                "showDelay": 0,
                "hideDelay": 100,
                "enterable": false,
                "confine": false,
                "appendToBody": false,
                "transitionDuration": 0.4,
                "formatter": "\u963b\u529b\u7ebf: y = -0.0968x + 42.5726 (\u8bc4\u5206: 92)",
                "textStyle": {
                    "fontSize": 14
                },
                "borderWidth": 0,
                "padding": 5,
                "order": "seriesAsc"
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "bar",
            "name": "\u6210\u4ea4\u91cf",
            "xAxisIndex": 1,
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                207299,
                349610,
                219380,
                231809,
                197230,
                328319,
                278807,
                416036,
                406193,
                274322,
                383007,
                547348,
                469734,
                385262,
                288263,
                215032,
                247003,
                298929,
                225391,
                462650,
                327067,
                289098,
                227783,
                388667,
                516959,
                378968,
                283435,
                277364,
                326158,
                265458,
                222112,
                254647,
                281070,
                216939,
                199867,
                278791,
                316968,
                266296,
                277305,
                219502,
                181976,
                331694,
                201444,
                174678,
                168122,
                316827,
                172195,
                212743,
                179339,
                240125,
                184919,
                117602,
                166414,
                117683,
                350325,
                398904,
                355222,
                237893,
                188353,
                145922,
                185125,
                149026,
                180862,
                189807,
                380946,
                432029,
                244107,
                201872,
                190801,
                246013,
                226021,
                414834,
                357451,
                281131,
                210618,
                242297,
                216940,
                382295,
                190393,
                164803,
                114676,
                110990,
                106588,
                127993,
                117135,
                120848,
                112683,
                92938,
                162304,
                156624,
                119018,
                339485,
                409864,
                331684,
                354253,
                248825,
                190245,
                417268,
                316504,
                446773,
                287682,
                413520,
                353719,
                325683,
                477325,
                289700,
                373856,
                271337,
                304790,
                305071,
                184772,
                151516,
                144081,
                327603,
                203583,
                434912,
                268478,
                311423,
                205321,
                367926
            ],
            "realtimeSort": false,
            "showBackground": false,
            "stackStrategy": "samesign",
            "cursor": "pointer",
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": false,
                "margin": 8,
                "valueAnimation": false
            },
            "itemStyle": {
                "color":                     function(params) {                        var colorList;                        if (params.data >= 0) {                            colorList = '#ef232a';                        } else {                            colorList = '#14b143';                        }                        return colorList;                    }                    
            }
        }
    ],
    "legend": [
        {
            "data": [
                "K\u7ebf",
                "MA5",
                "MA10",
                "MA20",
                "MA30",
                "\u652f\u6491\u7ebf (3\u70b9, \u4e0a\u5347, \u6781\u5f3a)",
                "\u963b\u529b\u7ebf (3\u70b9, \u4e0b\u964d, \u6781\u5f3a)"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 20,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10,
            "left": "center",
            "bottom": "0%",
            "orient": "horizontal"
        },
        {
            "data": [
                "\u6210\u4ea4\u91cf"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "xAxis": [
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": false,
                "onZeroAxisIndex": 0
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 20,
            "boundaryGap": false,
            "min": "dataMin",
            "max": "dataMax",
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14"
            ]
        },
        {
            "type": "category",
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0,
            "data": [
                "2025-01-13",
                "2025-01-14",
                "2025-01-15",
                "2025-01-16",
                "2025-01-17",
                "2025-01-20",
                "2025-01-21",
                "2025-01-22",
                "2025-01-23",
                "2025-01-24",
                "2025-01-27",
                "2025-02-05",
                "2025-02-06",
                "2025-02-07",
                "2025-02-10",
                "2025-02-11",
                "2025-02-12",
                "2025-02-13",
                "2025-02-14",
                "2025-02-17",
                "2025-02-18",
                "2025-02-19",
                "2025-02-20",
                "2025-02-21",
                "2025-02-24",
                "2025-02-25",
                "2025-02-26",
                "2025-02-27",
                "2025-02-28",
                "2025-03-03",
                "2025-03-04",
                "2025-03-05",
                "2025-03-06",
                "2025-03-07",
                "2025-03-10",
                "2025-03-11",
                "2025-03-12",
                "2025-03-13",
                "2025-03-14",
                "2025-03-17",
                "2025-03-18",
                "2025-03-19",
                "2025-03-20",
                "2025-03-21",
                "2025-03-24",
                "2025-03-25",
                "2025-03-26",
                "2025-03-27",
                "2025-03-28",
                "2025-03-31",
                "2025-04-01",
                "2025-04-02",
                "2025-04-03",
                "2025-04-07",
                "2025-04-08",
                "2025-04-09",
                "2025-04-10",
                "2025-04-11",
                "2025-04-14",
                "2025-04-15",
                "2025-04-16",
                "2025-04-17",
                "2025-04-18",
                "2025-04-21",
                "2025-04-22",
                "2025-04-23",
                "2025-04-24",
                "2025-04-25",
                "2025-04-28",
                "2025-04-29",
                "2025-04-30",
                "2025-05-06",
                "2025-05-07",
                "2025-05-08",
                "2025-05-09",
                "2025-05-12",
                "2025-05-13",
                "2025-05-14",
                "2025-05-15",
                "2025-05-16",
                "2025-05-19",
                "2025-05-20",
                "2025-05-21",
                "2025-05-22",
                "2025-05-23",
                "2025-05-26",
                "2025-05-27",
                "2025-05-28",
                "2025-05-29",
                "2025-05-30",
                "2025-06-03",
                "2025-06-04",
                "2025-06-05",
                "2025-06-06",
                "2025-06-09",
                "2025-06-10",
                "2025-06-11",
                "2025-06-12",
                "2025-06-13",
                "2025-06-16",
                "2025-06-17",
                "2025-06-18",
                "2025-06-19",
                "2025-06-20",
                "2025-06-23",
                "2025-06-24",
                "2025-06-25",
                "2025-06-26",
                "2025-06-27",
                "2025-06-30",
                "2025-07-01",
                "2025-07-02",
                "2025-07-03",
                "2025-07-04",
                "2025-07-07",
                "2025-07-08",
                "2025-07-09",
                "2025-07-10",
                "2025-07-11",
                "2025-07-14"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": true,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        },
        {
            "name": "\u6210\u4ea4\u91cf",
            "show": true,
            "scale": true,
            "nameLocation": "middle",
            "nameGap": 40,
            "nameRotate": 90,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "animation": true,
            "animationThreshold": 2000,
            "animationDuration": 1000,
            "animationEasing": "cubicOut",
            "animationDelay": 0,
            "animationDurationUpdate": 300,
            "animationEasingUpdate": "cubicOut",
            "animationDelayUpdate": 0
        }
    ],
    "title": [
        {
            "show": true,
            "text": "\u82f1\u7ef4\u514b(002837) K\u7ebf\u56fe (\u6574\u4f53\u8d8b\u52bf: \u4e0a\u5347)",
            "target": "blank",
            "subtarget": "blank",
            "left": "center",
            "top": "1%",
            "padding": [
                10,
                0,
                0,
                0
            ],
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        },
        {
            "show": true,
            "target": "blank",
            "subtarget": "blank",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false
        }
    ],
    "toolbox": {
        "show": true,
        "orient": "horizontal",
        "itemSize": 15,
        "itemGap": 10,
        "left": "80%",
        "right": "5%",
        "top": "top",
        "feature": {
            "saveAsImage": {},
            "dataZoom": {},
            "dataView": {},
            "restore": {}
        }
    },
    "dataZoom": [
        {
            "show": true,
            "type": "inside",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter",
            "disabled": false,
            "zoomOnMouseWheel": true,
            "moveOnMouseMove": true,
            "moveOnMouseWheel": true,
            "preventDefaultMouseMove": true
        },
        {
            "show": true,
            "type": "slider",
            "showDetail": true,
            "showDataShadow": true,
            "realtime": true,
            "start": 0,
            "end": 100,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        },
        [
            {
                "show": true,
                "type": "inside",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter",
                "disabled": false,
                "zoomOnMouseWheel": true,
                "moveOnMouseMove": true,
                "moveOnMouseWheel": true,
                "preventDefaultMouseMove": true
            },
            {
                "show": true,
                "type": "slider",
                "showDetail": true,
                "showDataShadow": true,
                "realtime": true,
                "start": 0,
                "end": 100,
                "orient": "horizontal",
                "zoomLock": false,
                "filterMode": "filter"
            }
        ]
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "10%",
            "right": "8%",
            "height": "60%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "10%",
            "top": "75%",
            "right": "8%",
            "height": "20%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1,
            "shadowOffsetX": 0,
            "shadowOffsetY": 0
        }
    ]
};
        chart_8b4dc3588d2648d29afbbc96838cd4dc.setOption(option_8b4dc3588d2648d29afbbc96838cd4dc);
            window.addEventListener('resize', function(){
                chart_8b4dc3588d2648d29afbbc96838cd4dc.resize();
            })
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 在页面加载完成后执行额外的调整
            setTimeout(function() {
                // 处理标题元素
                var titleElements = document.querySelectorAll('.title');
                titleElements.forEach(function(el) {
                    el.classList.add('title-text');
                });
                
                // 处理图例元素
                var legendElements = document.querySelectorAll('.legend');
                legendElements.forEach(function(el) {
                    el.style.paddingTop = '15px';
                });
                
                // 处理图例项
                var legendItems = document.querySelectorAll('.legend-item');
                legendItems.forEach(function(el) {
                    el.style.margin = '0 10px';
                });
            }, 500);
        });
    </script>
</body>
</html>
