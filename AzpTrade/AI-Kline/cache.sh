#!/bin/bash

# AI看线 缓存管理脚本

cd "$(dirname "${BASH_SOURCE[0]}")"

show_usage() {
    echo "📦 AI看线 缓存管理工具"
    echo ""
    echo "使用方法:"
    echo "  ./cache.sh list    - 列出所有缓存文件"
    echo "  ./cache.sh clean   - 清理过期缓存 (>2小时)"
    echo "  ./cache.sh info    - 显示缓存统计信息"
    echo "  ./cache.sh clear   - 清理所有缓存 (谨慎使用)"
    echo ""
}

case "$1" in
    list)
        echo "📋 执行: python cache_manager.py list"
        python cache_manager.py list
        ;;
    clean)
        echo "🧹 执行: python cache_manager.py clean"
        python cache_manager.py clean
        ;;
    info)
        echo "📊 执行: python cache_manager.py info"
        python cache_manager.py info
        ;;
    clear)
        echo "⚠️  清理所有缓存文件..."
        python cache_manager.py clean --force
        ;;
    ""|help)
        show_usage
        ;;
    *)
        echo "❌ 无效参数: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac 