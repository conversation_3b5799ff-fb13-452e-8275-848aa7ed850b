#!/bin/bash

# 自签名SSL证书生成脚本 (仅用于测试)

set -e

echo "🔒 自签名SSL证书生成脚本"
echo "==============================="
echo "⚠️  注意: 此脚本生成的证书仅用于测试，生产环境请使用Let's Encrypt"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo权限运行此脚本"
    exit 1
fi

DOMAIN="aikline.haidian666.com"
SSL_DIR="/etc/ssl"
CERT_DIR="$SSL_DIR/certs"
KEY_DIR="$SSL_DIR/private"

# 创建目录
mkdir -p "$CERT_DIR" "$KEY_DIR"

echo "📋 生成自签名证书..."
echo "域名: $DOMAIN"

# 生成私钥
openssl genrsa -out "$KEY_DIR/$DOMAIN.key" 2048

# 生成证书签名请求
openssl req -new -key "$KEY_DIR/$DOMAIN.key" -out "/tmp/$DOMAIN.csr" -subj "/C=CN/ST=Beijing/L=Beijing/O=AI-Kline/OU=IT/CN=$DOMAIN"

# 生成自签名证书（有效期1年）
openssl x509 -req -days 365 -in "/tmp/$DOMAIN.csr" -signkey "$KEY_DIR/$DOMAIN.key" -out "$CERT_DIR/$DOMAIN.crt"

# 设置权限
chmod 600 "$KEY_DIR/$DOMAIN.key"
chmod 644 "$CERT_DIR/$DOMAIN.crt"

# 清理临时文件
rm -f "/tmp/$DOMAIN.csr"

echo "✅ SSL证书生成完成!"
echo "证书文件: $CERT_DIR/$DOMAIN.crt"
echo "私钥文件: $KEY_DIR/$DOMAIN.key"
echo ""
echo "⚠️  浏览器警告:"
echo "由于是自签名证书，浏览器会显示安全警告"
echo "测试时可以点击'高级'并选择'继续访问'"
echo ""
echo "🔄 现在可以重启nginx:"
echo "sudo systemctl restart nginx" 