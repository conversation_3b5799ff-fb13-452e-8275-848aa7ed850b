#!/bin/bash

# 系统端口使用情况查询脚本

echo "🔍 系统端口使用情况查询"
echo "========================================="
echo ""

# 显示TCP监听端口
echo "📡 TCP监听端口 (服务端口):"
echo "----------------------------------------"
printf "%-8s %-22s %-15s %-20s %s\n" "端口" "绑定地址" "状态" "进程ID" "程序名称"
echo "----------------------------------------"
netstat -tlnp | grep LISTEN | while read line; do
    proto=$(echo $line | awk '{print $1}')
    local_addr=$(echo $line | awk '{print $4}')
    port=$(echo $local_addr | cut -d: -f2)
    bind_addr=$(echo $local_addr | cut -d: -f1)
    state=$(echo $line | awk '{print $6}')
    pid_prog=$(echo $line | awk '{print $7}')
    
    if [ "$bind_addr" = "0.0.0.0" ]; then
        bind_desc="所有接口"
    elif [ "$bind_addr" = "127.0.0.1" ]; then
        bind_desc="仅本地"
    elif [ "$bind_addr" = ":::" ]; then
        bind_desc="IPv6所有接口"
    else
        bind_desc="$bind_addr"
    fi
    
    if [ "$pid_prog" = "-" ]; then
        process_info="系统进程"
    else
        process_info="$pid_prog"
    fi
    
    printf "%-8s %-22s %-15s %-20s\n" "$port" "$bind_desc" "$state" "$process_info"
done

echo ""

# 显示UDP监听端口
echo "📡 UDP监听端口:"
echo "----------------------------------------"
printf "%-8s %-22s %-20s %s\n" "端口" "绑定地址" "进程ID" "程序名称"
echo "----------------------------------------"
netstat -ulnp | grep -v "Active Internet" | grep -v "Proto" | while read line; do
    if [ -n "$line" ]; then
        proto=$(echo $line | awk '{print $1}')
        local_addr=$(echo $line | awk '{print $4}')
        port=$(echo $local_addr | cut -d: -f2)
        bind_addr=$(echo $local_addr | cut -d: -f1)
        pid_prog=$(echo $line | awk '{print $6}')
        
        if [ "$bind_addr" = "0.0.0.0" ]; then
            bind_desc="所有接口"
        elif [ "$bind_addr" = "127.0.0.1" ]; then
            bind_desc="仅本地"
        elif [ "$bind_addr" = ":::" ]; then
            bind_desc="IPv6所有接口"
        else
            bind_desc="$bind_addr"
        fi
        
        if [ "$pid_prog" = "-" ]; then
            process_info="系统进程"
        else
            process_info="$pid_prog"
        fi
        
        printf "%-8s %-22s %-20s %s\n" "$port" "$bind_desc" "$process_info"
    fi
done

echo ""

# 显示常用端口分类
echo "🔧 系统服务端口分析:"
echo "----------------------------------------"

# 检查常用端口
check_port() {
    local port=$1
    local service=$2
    local result=$(netstat -tlnp | grep ":$port ")
    if [ -n "$result" ]; then
        local pid_prog=$(echo $result | awk '{print $7}')
        echo "✅ $port - $service ($pid_prog)"
    else
        echo "⭕ $port - $service (未使用)"
    fi
}

echo ""
echo "📋 Web服务端口:"
check_port "80" "HTTP"
check_port "443" "HTTPS"
check_port "8000" "HTTP备用"
check_port "8080" "HTTP备用"
check_port "8018" "AI看线服务"

echo ""
echo "📋 系统服务端口:"
check_port "22" "SSH"
check_port "53" "DNS"
check_port "21" "FTP"
check_port "25" "SMTP"

echo ""
echo "📋 数据库端口:"
check_port "3306" "MySQL"
check_port "5432" "PostgreSQL"
check_port "6379" "Redis"
check_port "27017" "MongoDB"

echo ""

# 显示端口占用统计
echo "📊 端口使用统计:"
echo "----------------------------------------"
total_tcp=$(netstat -tln | grep LISTEN | wc -l)
total_udp=$(netstat -uln | grep -v "Active\|Proto" | wc -l)
external_tcp=$(netstat -tln | grep "0.0.0.0:" | wc -l)
local_tcp=$(netstat -tln | grep "127.0.0.1:" | wc -l)

echo "TCP监听端口总数: $total_tcp"
echo "UDP监听端口总数: $total_udp"
echo "外网可访问TCP端口: $external_tcp"
echo "仅本地访问TCP端口: $local_tcp"

echo ""

# 显示正在使用的自定义端口 (1024以上)
echo "🔍 自定义应用端口 (>1024):"
echo "----------------------------------------"
netstat -tlnp | grep LISTEN | awk '{print $4 " " $7}' | while read addr prog; do
    port=$(echo $addr | cut -d: -f2)
    if [ "$port" -gt 1024 ] 2>/dev/null; then
        bind=$(echo $addr | cut -d: -f1)
        if [ "$bind" = "0.0.0.0" ]; then
            access="外网可访问"
        elif [ "$bind" = "127.0.0.1" ]; then
            access="仅本地"
        else
            access="特定接口"
        fi
        echo "端口 $port - $access - $prog"
    fi
done

echo ""

# 安全建议
echo "🔒 安全建议:"
echo "----------------------------------------"
echo "1. 关闭不必要的服务端口"
echo "2. 仅本地使用的服务绑定到127.0.0.1"
echo "3. 外网服务使用防火墙限制访问"
echo "4. 定期检查端口使用情况"
echo "5. 避免使用默认端口降低被攻击风险"

echo ""
echo "💡 端口管理命令:"
echo "----------------------------------------"
echo "查看特定端口: lsof -i :端口号"
echo "杀死端口进程: kill -9 进程ID"
echo "实时监控连接: watch 'netstat -tlnp'" 