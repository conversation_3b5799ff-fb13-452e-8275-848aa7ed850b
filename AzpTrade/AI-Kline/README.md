# AI看线 - A股技术分析与AI预测工具

基于人工智能的A股股票技术分析工具，提供完整的技术指标分析、可视化图表和AI预测功能。

## ✨ 主要功能

- 📊 **股票数据获取**: 实时获取A股股票行情、财务数据和新闻信息
- 📈 **技术指标分析**: 158种技术指标支持（基于TA-Lib 0.6.3）
- 🎨 **可视化图表**: 生成专业的K线图和技术指标图表
- 🤖 **AI智能分析**: 集成OpenAI进行智能分析和预测
- 🌐 **Web界面**: 用户友好的网页界面
- 🔄 **常驻服务**: 支持后台运行的Web服务

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository_url>
cd AI-Kline

# 创建虚拟环境
cd ..
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或者 .venv\Scripts\activate  # Windows

# 安装依赖
cd AI-Kline
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件：
```bash
# OpenRouter API配置 (可选，用于AI分析)
OPENROUTER_API_KEY=your_api_key_here
```

### 3. 启动服务

#### 🟢 常驻模式（推荐）

```bash
# 启动服务
./service.sh start

# 查看状态
./service.sh status

# 查看日志
./service.sh logs

# 停止服务
./service.sh stop

# 重启服务
./service.sh restart
```

#### 🟡 临时模式

```bash
# 直接运行
python web_app.py

# 或使用完整启动脚本
./start_web.sh start
```

### 4. 访问服务

- 🌐 **Web界面**: http://localhost:5888
- 📱 **移动端友好**: 响应式设计，支持手机访问

## 📋 命令行使用

```bash
# 分析单只股票
python main.py --stock_code 000001 --period 30

# 批量分析
python main.py --batch_file stocks.txt

# 指定输出路径
python main.py --stock_code 000001 --output_path ./custom_output
```

## 📁 项目结构

```
AI-Kline/
├── main.py              # 主程序入口
├── web_app.py           # Web服务应用
├── service.sh           # 服务管理脚本（简化版）
├── start_web.sh         # 完整启动脚本（常驻模式）
├── requirements.txt     # Python依赖
├── .env                 # 环境变量配置
├── modules/             # 核心模块
│   ├── data_fetcher.py      # 数据获取
│   ├── technical_analyzer.py # 技术指标分析
│   ├── visualizer.py        # 可视化
│   └── ai_analyzer.py       # AI分析
├── templates/           # HTML模板
├── static/             # 静态资源
├── output/             # 输出文件
│   └── charts/         # 图表文件
└── logs/               # 服务日志
    ├── web_service.log     # 运行日志
    └── web_service_error.log # 错误日志
```

## 🔧 技术栈

- **后端**: Python 3.12, Flask
- **数据源**: AkShare (A股数据)
- **技术分析**: TA-Lib 0.6.3 (158种指标)
- **可视化**: Matplotlib, Pyecharts
- **AI分析**: OpenAI GPT-4
- **前端**: Bootstrap 5, JavaScript

## 📈 支持的技术指标

### 趋势指标
- SMA, EMA, WMA (移动平均线)
- MACD (指数平滑移动平均线)
- Bollinger Bands (布林带)

### 动量指标  
- RSI (相对强弱指数)
- Stochastic (随机指标)
- Williams %R

### 成交量指标
- OBV (能量潮)
- Volume Rate (成交量比率)

### 更多指标
总计158种TA-Lib支持的所有技术指标

## 🔧 常驻服务管理

### 服务状态管理

```bash
# 查看服务状态
./service.sh status

# 实时查看日志
tail -f logs/web_service.log

# 查看错误日志
tail -f logs/web_service_error.log
```

### 开机自启动（可选）

添加到系统服务：
```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/ai-kline.service
```

服务文件内容：
```ini
[Unit]
Description=AI Kline Web Service
After=network.target

[Service]
Type=forking
User=root
WorkingDirectory=/root/yf/ai/yfstockquant/AI-Kline
ExecStart=/root/yf/ai/yfstockquant/AI-Kline/service.sh start
ExecStop=/root/yf/ai/yfstockquant/AI-Kline/service.sh stop
Restart=on-failure

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl enable ai-kline
sudo systemctl start ai-kline
```

## 🐛 故障排除

### TA-Lib安装问题

如果遇到TA-Lib安装错误，手动安装：

```bash
# Ubuntu/Debian
sudo apt-get install -y build-essential wget
wget https://github.com/TA-Lib/ta-lib/releases/download/v0.6.4/ta-lib-0.6.4-src.tar.gz
tar -xzf ta-lib-0.6.4-src.tar.gz
cd ta-lib-0.6.4
./configure --prefix=/usr
make && sudo make install
pip install ta-lib
```

### 中文字体问题

```bash
# 安装中文字体
sudo apt-get install -y fonts-wqy-zenhei fonts-wqy-microhei
fc-cache -fv
```

### 端口占用问题

```bash
# 检查端口占用
lsof -i :5888

# 杀死占用进程
kill -9 <PID>
```

## 📞 技术支持

- 🐛 **Bug报告**: 请提交详细的错误日志和复现步骤
- 💡 **功能建议**: 欢迎提出改进建议
- 📖 **使用问题**: 查看项目文档或提交Issue

## 📄 许可证

本项目采用 MIT 许可证。

---

**⚠️ 免责声明**: 本工具仅供学习和研究使用，不构成投资建议。投资有风险，请谨慎决策。