/* 基础样式 */
body {
    background-color: #f8f9fa;
    font-family: "Microsoft YaHei", "Segoe UI", Tahoma, sans-serif;
}

/* 头部样式 */
header {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 卡片样式 */
.card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.card-header {
    font-weight: bold;
}

/* 分析结果文本区域 */
#analysisText {
    font-family: "Microsoft YaHei", sans-serif;
    font-size: 0.8rem;
    line-height: 1.5;
    white-space: pre-wrap;
    max-height: 1200px;
    overflow-y: auto;
    color: #333;
    padding-right: 10px;
}

/* 分析结果文本区域 */
.analysis-content {
    font-family: "Microsoft YaHei", sans-serif;
    font-size: 0.95rem;
    line-height: 1.6;
    max-height: 1200px;
    overflow-y: auto;
    color: #333;
    padding-right: 10px;
}

.analysis-content::-webkit-scrollbar {
    width: 6px;
}

.analysis-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.analysis-content::-webkit-scrollbar-thumb {
    background: #aaa;
    border-radius: 10px;
}

.analysis-content::-webkit-scrollbar-thumb:hover {
    background: #888;
}

.analysis-content h1 {
    font-size: 1.5rem;
    color: #0d6efd;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.analysis-content h2 {
    font-size: 1.3rem;
    color: #198754;
}

.analysis-content h3 {
    font-size: 1.15rem;
    color: #fd7e14;
}

.analysis-content h4 {
    font-size: 1.05rem;
    color: #6f42c1;
}

.analysis-content blockquote {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
}

.analysis-content ul {
    padding-left: 1.5rem;
}

.analysis-content p {
    margin-bottom: 0.7rem;
}

.analysis-content .badge {
    font-weight: normal;
    font-size: 0.85em;
}

.analysis-content table {
    font-size: 0.9rem;
}

.analysis-content table th {
    background-color: #f1f8ff;
}

.analysis-content a {
    color: #0d6efd;
    border-bottom: 1px dotted;
}

.analysis-content a:hover {
    border-bottom-style: solid;
}

/* 高亮股票数据 */
.stock-highlight {
    font-weight: bold;
    border-bottom: 1px dashed #ccc;
    color: #dc3545;
    padding: 0 2px;
    background-color: rgba(220, 53, 69, 0.05);
    border-radius: 2px;
}

/* 图表容器 */
.chart-container {
    margin-bottom: 30px;
    text-align: center;
    overflow: hidden;
    position: relative;
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-container iframe {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    transition: all 0.3s ease;
    max-width: 100%;
    overflow: hidden;
}

.chart-container iframe:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.chart-container a.btn {
    transition: all 0.2s ease;
}

.chart-container a.btn:hover {
    transform: translateY(-2px);
}

/* 表格样式 */
.stock-info-table {
    width: 100%;
}

.stock-info-table th {
    width: 40%;
    font-weight: 600;
    color: #495057;
}

/* 加载动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.spinner-border {
    animation: spinner-border 1s linear infinite, pulse 2s ease-in-out infinite;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .row {
        flex-direction: column-reverse;
    }
    
    .chart-container iframe {
        height: 600px !important; /* 在中等屏幕上减小高度 */
    }
}

@media (max-width: 576px) {
    .chart-container iframe {
        height: 500px !important; /* 在更小的屏幕上进一步减小高度 */
    }
} 