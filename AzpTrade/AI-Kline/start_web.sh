#!/bin/bash

# AI看线 Web服务启动脚本 - 常驻模式

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/web_service.pid"
LOG_FILE="$SCRIPT_DIR/logs/web_service.log"
ERROR_LOG="$SCRIPT_DIR/logs/web_service_error.log"
LOCK_FILE="$SCRIPT_DIR/web_service.lock"

# 创建日志目录
mkdir -p "$SCRIPT_DIR/logs"

# 显示使用说明
show_usage() {
    echo "AI看线 Web服务管理脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 start   - 启动服务"
    echo "  $0 stop    - 停止服务"
    echo "  $0 restart - 重启服务"
    echo "  $0 status  - 查看服务状态"
    echo "  $0 logs    - 查看服务日志"
    echo ""
}

# 检查服务状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0  # 服务正在运行
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 服务未运行
        fi
    else
        return 1  # 服务未运行
    fi
}

# 启动服务
start_service() {
    echo "🚀 启动AI看线 Web服务..."
    
    # 检查是否已经在运行
    if check_status; then
        PID=$(cat "$PID_FILE")
        echo "❌ 服务已在运行 (PID: $PID)"
        echo "📱 访问地址: http://localhost:8018"
        return 1
    fi
    
    # 直接使用系统Python环境（已去除虚拟环境依赖）
    
    # 检查依赖
    if ! python -c "import flask" 2>/dev/null; then
        echo "❌ 缺少依赖，正在安装..."
        pip install -r "$SCRIPT_DIR/requirements.txt"
    fi
    
    # 检查环境变量
    if [ ! -f "$SCRIPT_DIR/.env" ]; then
        echo "⚠️  未找到.env文件，AI分析功能将无法使用"
        echo "   请创建.env文件并添加："
        echo "   OPENROUTER_API_KEY=your_api_key_here"
        echo ""
    fi
    
    # 创建锁文件
    touch "$LOCK_FILE"
    
    # 启动服务（后台模式）
    echo "✅ 启动Web服务..."
    echo "📱 访问地址: http://localhost:8018"
    echo "📝 日志文件: $LOG_FILE"
    echo "🔄 服务将在后台运行..."
    
    cd "$SCRIPT_DIR"
    nohup python web_app.py > "$LOG_FILE" 2> "$ERROR_LOG" &
    
    # 保存PID
    echo $! > "$PID_FILE"
    
    # 等待服务启动
    sleep 2
    
    if check_status; then
        PID=$(cat "$PID_FILE")
        echo "✅ 服务启动成功 (PID: $PID)"
        echo ""
        echo "管理命令:"
        echo "  查看状态: $0 status"
        echo "  查看日志: $0 logs"
        echo "  停止服务: $0 stop"
    else
        echo "❌ 服务启动失败，请查看错误日志: $ERROR_LOG"
        rm -f "$LOCK_FILE"
        return 1
    fi
}

# 停止服务
stop_service() {
    echo "🛑 停止AI看线 Web服务..."
    
    if check_status; then
        PID=$(cat "$PID_FILE")
        echo "正在停止服务 (PID: $PID)..."
        
        # 发送TERM信号
        kill "$PID" 2>/dev/null
        
        # 等待进程结束
        for i in {1..10}; do
            if ! ps -p "$PID" > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done
        
        # 如果进程仍在运行，强制杀死
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "强制停止服务..."
            kill -9 "$PID" 2>/dev/null
        fi
        
        # 清理文件
        rm -f "$PID_FILE" "$LOCK_FILE"
        echo "✅ 服务已停止"
    else
        echo "⚠️  服务未在运行"
    fi
}

# 重启服务
restart_service() {
    echo "🔄 重启AI看线 Web服务..."
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
show_status() {
    echo "AI看线 Web服务状态:"
    echo "===================="
    
    if check_status; then
        PID=$(cat "$PID_FILE")
        echo "✅ 状态: 运行中"
        echo "📍 PID: $PID"
        echo "🌐 访问地址: http://localhost:8018"
        echo "📝 日志文件: $LOG_FILE"
        echo "⚠️  错误日志: $ERROR_LOG"
        
        # 显示进程信息
        echo ""
        echo "进程信息:"
        ps -p "$PID" -o pid,ppid,cmd,etime,cpu,mem 2>/dev/null || echo "无法获取进程信息"
    else
        echo "❌ 状态: 未运行"
    fi
    echo ""
}

# 查看日志
show_logs() {
    echo "📝 AI看线 Web服务日志:"
    echo "======================"
    
    if [ -f "$LOG_FILE" ]; then
        echo "最近20行日志:"
        tail -20 "$LOG_FILE"
    else
        echo "📝 日志文件不存在: $LOG_FILE"
    fi
    
    echo ""
    echo "错误日志:"
    if [ -f "$ERROR_LOG" ]; then
        tail -10 "$ERROR_LOG"
    else
        echo "📝 错误日志文件不存在: $ERROR_LOG"
    fi
}

# 主逻辑
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    *)
        show_usage
        exit 1
        ;;
esac 