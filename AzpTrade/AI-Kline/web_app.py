import os
import json
import re
import time
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, send_from_directory
from modules.data_fetcher import StockDataFetcher
from modules.technical_analyzer import TechnicalAnalyzer
from modules.visualizer import Visualizer
from modules.ai_analyzer import AIAnalyzer
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = Flask(__name__, static_folder='static', template_folder='templates')

# 确保输出目录存在
os.makedirs('./output', exist_ok=True)
os.makedirs('./output/charts', exist_ok=True)

# 初始化各模块
data_fetcher = StockDataFetcher()
technical_analyzer = TechnicalAnalyzer()
visualizer = Visualizer()
ai_analyzer = AIAnalyzer()

# 缓存配置
CACHE_EXPIRE_HOURS = 2  # 缓存过期时间（小时）

def check_cache_validity(stock_code, period, save_path='./output'):
    """检查缓存是否有效（2小时内）"""
    try:
        # 检查分析结果文件
        result_file = os.path.join(save_path, f"{stock_code}_analysis_result.txt")
        
        if not os.path.exists(result_file):
            return False, "分析结果文件不存在"
        
        # 检查图表文件是否存在
        charts_dir = os.path.join(save_path, 'charts')
        chart_files = []
        if os.path.exists(charts_dir):
            chart_files = [f for f in os.listdir(charts_dir) 
                          if f.startswith(stock_code) and (f.endswith('.png') or f.endswith('.html'))]
        
        if not chart_files:
            return False, "图表文件不存在"
        
        # 检查文件修改时间
        file_mtime = os.path.getmtime(result_file)
        file_time = datetime.fromtimestamp(file_mtime)
        expire_time = datetime.now() - timedelta(hours=CACHE_EXPIRE_HOURS)
        
        if file_time < expire_time:
            return False, f"缓存已过期（超过{CACHE_EXPIRE_HOURS}小时）"
        
        return True, f"缓存有效（生成于{file_time.strftime('%Y-%m-%d %H:%M:%S')}）"
    
    except Exception as e:
        return False, f"检查缓存时出错: {str(e)}"

def load_cached_result(stock_code, save_path='./output'):
    """加载缓存的分析结果"""
    try:
        # 读取分析结果
        result_file = os.path.join(save_path, f"{stock_code}_analysis_result.txt")
        with open(result_file, 'r', encoding='utf-8') as f:
            analysis_result = f.read()
        
        # 获取图表文件列表
        charts_dir = os.path.join(save_path, 'charts')
        chart_files = []
        if os.path.exists(charts_dir):
            chart_files = [f for f in os.listdir(charts_dir) 
                          if f.startswith(stock_code) and (f.endswith('.png') or f.endswith('.html'))]
        
        return {
            'success': True,
            'stock_code': stock_code,
            'charts': chart_files,
            'analysis_result': analysis_result,
            'from_cache': True,
            'cache_time': datetime.fromtimestamp(os.path.getmtime(result_file)).strftime('%Y-%m-%d %H:%M:%S')
        }
    
    except Exception as e:
        return None

def clean_expired_cache(save_path='./output'):
    """清理过期的缓存文件"""
    try:
        expire_time = datetime.now() - timedelta(hours=CACHE_EXPIRE_HOURS)
        
        # 清理分析结果文件
        for file in os.listdir(save_path):
            if file.endswith('_analysis_result.txt'):
                file_path = os.path.join(save_path, file)
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < expire_time:
                    os.remove(file_path)
                    print(f"🗑️ 清理过期缓存文件: {file}")
        
        # 清理图表文件
        charts_dir = os.path.join(save_path, 'charts')
        if os.path.exists(charts_dir):
            for file in os.listdir(charts_dir):
                if file.endswith('.png') or file.endswith('.html'):
                    file_path = os.path.join(charts_dir, file)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_time < expire_time:
                        os.remove(file_path)
                        print(f"🗑️ 清理过期图表文件: {file}")
    
    except Exception as e:
        print(f"⚠️ 清理缓存时出错: {e}")

def format_stock_info_value(key, value):
    """格式化股票信息的值"""
    if not value or value == '-':
        return value
    
    try:
        # 总市值和流通市值 - 转换为亿元格式
        if key in ['总市值', '流通市值']:
            # 如果值包含数字，提取数字部分
            num_str = re.sub(r'[^\d.]', '', str(value))
            if num_str:
                num = float(num_str)
                # 转换为亿元
                yi = num / 100000000
                return f"{yi:.2f}亿"
        
        # 上市时间 - 格式化为 YYYY-MM-DD
        elif key == '上市时间':
            date_str = str(value)
            # 如果是8位数字格式 (如: 20160303)
            if date_str.isdigit() and len(date_str) == 8:
                year = date_str[:4]
                month = date_str[4:6]
                day = date_str[6:8]
                return f"{year}-{month}-{day}"
        
        # 总股本和流通股 - 添加千分位分隔符
        elif key in ['总股本', '流通股']:
            # 提取数字部分
            num_str = re.sub(r'[^\d.]', '', str(value))
            if num_str:
                num = float(num_str)
                # 如果是整数，格式化为整数的千分位
                if num.is_integer():
                    return f"{int(num):,}"
                else:
                    return f"{num:,.2f}"
    
    except (ValueError, AttributeError):
        pass
    
    return value

@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/analyze', methods=['POST'])
def analyze():
    """分析股票"""
    data = request.form
    stock_code = data.get('stock_code')
    period = data.get('period', '1年')
    save_path = './output'
    
    if not stock_code:
        return jsonify({'error': '请输入股票代码'}), 400
    
    try:
        # 检查缓存
        cache_valid, cache_msg = check_cache_validity(stock_code, period, save_path)
        
        if cache_valid:
            print(f"📦 使用缓存结果 - {stock_code}: {cache_msg}")
            cached_result = load_cached_result(stock_code, save_path)
            if cached_result:
                return jsonify(cached_result)
        else:
            print(f"🔄 重新生成分析 - {stock_code}: {cache_msg}")
        
        # 清理过期缓存（后台任务）
        clean_expired_cache(save_path)
        
        # 获取股票数据
        stock_data = data_fetcher.fetch_stock_data(stock_code, period)
        
        if stock_data.empty:
            return jsonify({'error': f'未找到股票 {stock_code} 的数据'}), 404
        
        # 获取财务和新闻数据
        financial_data = data_fetcher.fetch_financial_data(stock_code)
        news_data = data_fetcher.fetch_news_data(stock_code)
        
        # 计算技术指标
        indicators = technical_analyzer.calculate_indicators(stock_data)
        
        # 生成可视化图表
        chart_path = visualizer.create_charts(stock_data, indicators, stock_code, save_path)
        
        # AI分析预测
        analysis_result = ai_analyzer.analyze(stock_data, indicators, financial_data, news_data, stock_code, save_path)
        
        # 保存分析结果
        result_path = os.path.join(save_path, f"{stock_code}_analysis_result.txt")
        with open(result_path, 'w', encoding='utf-8') as f:
            f.write(analysis_result)
        
        # 准备返回数据
        chart_files = []
        for file in os.listdir(os.path.join(save_path, 'charts')):
            if file.startswith(stock_code) and (file.endswith('.png') or file.endswith('.html')):
                chart_files.append(file)
        
        return jsonify({
            'success': True,
            'stock_code': stock_code,
            'charts': chart_files,
            'analysis_result': analysis_result,
            'from_cache': False,
            'cache_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    
    except Exception as e:
        return jsonify({'error': f'分析过程中出错: {str(e)}'}), 500

@app.route('/output/charts/<path:filename>')
def serve_chart(filename):
    """提供图表文件"""
    return send_from_directory('output/charts', filename)

@app.route('/stock_info/<stock_code>')
def get_stock_info(stock_code):
    """获取股票基本信息"""
    try:
        import akshare as ak
        stock_info = ak.stock_individual_info_em(symbol=stock_code)
        if not stock_info.empty:
            # 转换为字典并格式化值
            info_dict = {}
            for _, row in stock_info.iterrows():
                key = row['item']
                value = row['value']
                
                # 字段名称替换
                if key == '最新':
                    key = '最新价'
                
                # 格式化特定字段的值
                formatted_value = format_stock_info_value(key, value)
                info_dict[key] = formatted_value
            
            return jsonify({'success': True, 'data': info_dict})
        else:
            return jsonify({'error': f'未找到股票 {stock_code} 的信息'}), 404
    except Exception as e:
        return jsonify({'error': f'获取股票信息时出错: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=8018) 