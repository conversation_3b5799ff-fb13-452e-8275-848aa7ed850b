<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI看线 - A股技术分析与AI预测工具</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container-fluid">
        <header class="p-3 mb-4 bg-primary text-white">
            <div class="container">
                <div class="d-flex align-items-center justify-content-between">
                    <h1 class="mb-0">AI看线</h1>
                    <p class="mb-0">A股技术分析与AI预测工具</p>
                </div>
            </div>
        </header>

        <div class="container">
            <div class="row">
                <!-- 左侧表单区域 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">股票分析</h5>
                        </div>
                        <div class="card-body">
                            <form id="analyzeForm">
                                <div class="mb-3">
                                    <label for="stockCode" class="form-label">股票代码</label>
                                    <input type="text" class="form-control" id="stockCode" name="stock_code" placeholder="例如: 000001" required>
                                </div>
                                <div class="mb-3">
                                    <label for="period" class="form-label">分析周期</label>
                                    <select class="form-select" id="period" name="period">
                                        <option value="1年" selected>1年</option>
                                        <option value="6个月">6个月</option>
                                        <option value="3个月">3个月</option>
                                        <option value="1个月">1个月</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">开始分析</button>
                            </form>
                        </div>
                    </div>

                    <!-- 股票基本信息卡片 -->
                    <div id="stockInfoCard" class="card mt-4 d-none">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">股票基本信息</h5>
                        </div>
                        <div class="card-body" id="stockInfoBody">
                            <!-- 股票信息将通过JavaScript动态填充 -->
                        </div>
                    </div>
                </div>

                <!-- 右侧结果区域 -->
                <div class="col-md-8">
                    <!-- 加载中提示 -->
                    <div id="loadingIndicator" class="text-center my-5 d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在分析，请稍候...</p>
                    </div>

                    <!-- 图表区域 -->
                    <div id="chartsArea" class="d-none">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">交互式K线图</h5>
                            </div>
                            <div class="card-body" id="chartImages">
                                <!-- 图表将通过JavaScript动态填充 -->
                            </div>
                        </div>
                    </div>

                    <!-- AI分析结果 -->
                    <div id="analysisResult" class="card mb-4 d-none">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">AI分析结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="analysisText" class="analysis-content p-3 bg-light rounded"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="mt-5 p-3 bg-light text-center">
            <div class="container">
                <p class="mb-0">© 2025 AI看线 - A股技术分析与AI预测工具</p>
                <p class="mb-0 small text-muted">免责声明：本工具提供的分析和预测仅供参考，不构成任何投资建议。投资有风险，入市需谨慎。</p>
            </div>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html> 