#!/bin/bash

# AI看线 网络访问问题排查脚本

echo "🔍 AI看线 网络访问诊断工具"
echo "================================"

# 获取基本信息
EXTERNAL_IP=$(curl -s http://checkip.amazonaws.com 2>/dev/null || curl -s ifconfig.me 2>/dev/null || echo "未知")
INTERNAL_IP=$(ip route get 1 | awk '{print $7; exit}' 2>/dev/null || echo "未知")
SERVICE_PORT=8018

echo "📋 基本信息:"
echo "  外网IP: $EXTERNAL_IP"
echo "  内网IP: $INTERNAL_IP"  
echo "  服务端口: $SERVICE_PORT"
echo ""

# 检查服务状态
echo "🔧 服务状态检查:"
if netstat -tlnp | grep -q ":$SERVICE_PORT "; then
    echo "  ✅ 服务正在运行并监听端口 $SERVICE_PORT"
    PID=$(netstat -tlnp | grep ":$SERVICE_PORT " | awk '{print $7}' | cut -d'/' -f1)
    echo "  📍 进程ID: $PID"
else
    echo "  ❌ 服务未在端口 $SERVICE_PORT 上运行"
    echo "  💡 请先启动服务: ./service.sh start"
    exit 1
fi

# 检查本地访问
echo ""
echo "🏠 本地访问测试:"
LOCAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:$SERVICE_PORT 2>/dev/null)
if [ "$LOCAL_STATUS" = "200" ]; then
    echo "  ✅ 本地访问正常 (HTTP $LOCAL_STATUS)"
else
    echo "  ❌ 本地访问失败 (HTTP $LOCAL_STATUS)"
fi

# 检查防火墙规则
echo ""
echo "🔥 防火墙状态检查:"

# UFW检查
if command -v ufw >/dev/null; then
    UFW_STATUS=$(ufw status | head -1)
    echo "  UFW: $UFW_STATUS"
fi

# iptables检查
IPTABLES_RULE=$(iptables -L INPUT | grep -E "(8018|dpt:8018)" | head -1)
if [ -n "$IPTABLES_RULE" ]; then
    echo "  ✅ iptables已有8018端口规则"
else
    echo "  ⚠️  iptables无8018端口规则"
fi

# 外网访问测试
echo ""
echo "🌐 外网访问测试:"
echo "  正在测试外网访问..."
EXTERNAL_STATUS=$(timeout 10 curl -s -o /dev/null -w "%{http_code}" http://$EXTERNAL_IP:$SERVICE_PORT 2>/dev/null || echo "超时")

if [ "$EXTERNAL_STATUS" = "200" ]; then
    echo "  ✅ 外网访问正常!"
    echo "  🎉 可以通过 http://$EXTERNAL_IP:$SERVICE_PORT 访问"
elif [ "$EXTERNAL_STATUS" = "超时" ]; then
    echo "  ❌ 外网访问超时"
    echo "  🔒 可能原因: 云服务商安全组限制"
else
    echo "  ❌ 外网访问失败 (HTTP $EXTERNAL_STATUS)"
fi

echo ""
echo "📝 解决方案:"
echo "================================"

if [ "$EXTERNAL_STATUS" != "200" ]; then
    echo "❌ 外网无法访问，需要配置云服务商安全组:"
    echo ""
    echo "📋 腾讯云配置步骤:"
    echo "  1. 登录腾讯云控制台"
    echo "  2. 云服务器 > 安全组"
    echo "  3. 添加入站规则:"
    echo "     - 协议: TCP"
    echo "     - 端口: $SERVICE_PORT"  
    echo "     - 源地址: 0.0.0.0/0"
    echo "     - 策略: 允许"
    echo ""
    echo "📋 阿里云配置步骤:"
    echo "  1. 登录阿里云控制台"
    echo "  2. ECS > 网络与安全 > 安全组"
    echo "  3. 添加入方向规则:"
    echo "     - 协议: TCP"
    echo "     - 端口: $SERVICE_PORT/$SERVICE_PORT"
    echo "     - 授权对象: 0.0.0.0/0"
    echo ""
    echo "🔧 临时解决方案 (重启后失效):"
    echo "  sudo iptables -I INPUT -p tcp --dport $SERVICE_PORT -j ACCEPT"
    echo ""
    echo "🔒 安全建议:"
    echo "  - 生产环境建议限制访问IP范围"
    echo "  - 可以只允许特定IP访问，而不是0.0.0.0/0"
else
    echo "✅ 网络配置正常!"
    echo "🌐 外网访问地址: http://$EXTERNAL_IP:$SERVICE_PORT"
fi

echo ""
echo "📞 需要帮助?"
echo "  如果问题仍然存在，请提供以下信息:"
echo "  - 云服务商名称 (腾讯云/阿里云/AWS/其他)"
echo "  - 安全组配置截图"
echo "  - 本脚本的完整输出" 