#!/bin/bash

# AI看线 Nginx配置安装脚本

set -e

cd "$(dirname "${BASH_SOURCE[0]}")"

echo "🚀 AI看线 Nginx配置安装脚本"
echo "================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo权限运行此脚本"
    echo "   sudo ./setup_nginx.sh"
    exit 1
fi

# 检查nginx是否已安装
if ! command -v nginx >/dev/null 2>&1; then
    echo "📦 Nginx未安装，正在安装..."
    
    # 检测操作系统
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        apt update
        apt install -y nginx
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        yum install -y epel-release
        yum install -y nginx
    else
        echo "❌ 不支持的操作系统，请手动安装nginx"
        exit 1
    fi
    
    echo "✅ Nginx安装完成"
else
    echo "✅ Nginx已安装"
fi

# 检查AI看线服务是否运行
if ! netstat -tlnp 2>/dev/null | grep -q ":8018 "; then
    echo "⚠️  警告: AI看线服务未在端口8018上运行"
    echo "   请先启动服务: ./service.sh start"
    echo "   或者确认服务正在运行"
fi

# 备份现有nginx配置
NGINX_CONF_DIR="/etc/nginx"
BACKUP_DIR="/tmp/nginx_backup_$(date +%Y%m%d_%H%M%S)"

if [ -d "$NGINX_CONF_DIR/sites-available" ]; then
    # Debian/Ubuntu风格
    SITES_AVAILABLE="$NGINX_CONF_DIR/sites-available"
    SITES_ENABLED="$NGINX_CONF_DIR/sites-enabled"
    CONFIG_FILE="$SITES_AVAILABLE/aikline.haidian666.com"
elif [ -d "$NGINX_CONF_DIR/conf.d" ]; then
    # CentOS/RHEL风格
    SITES_AVAILABLE="$NGINX_CONF_DIR/conf.d"
    SITES_ENABLED="$NGINX_CONF_DIR/conf.d"
    CONFIG_FILE="$SITES_AVAILABLE/aikline.haidian666.com.conf"
else
    echo "❌ 无法确定nginx配置目录结构"
    exit 1
fi

echo "📁 配置目录: $SITES_AVAILABLE"

# 创建备份
mkdir -p "$BACKUP_DIR"
cp -r "$NGINX_CONF_DIR" "$BACKUP_DIR/"
echo "💾 配置已备份到: $BACKUP_DIR"

# 复制配置文件
echo "📋 安装nginx配置文件..."
cp nginx.conf "$CONFIG_FILE"

# 如果是Debian/Ubuntu，创建软链接启用站点
if [ -d "$NGINX_CONF_DIR/sites-enabled" ]; then
    ln -sf "$CONFIG_FILE" "$SITES_ENABLED/"
    echo "🔗 已启用站点配置"
fi

# 提示SSL证书配置
echo ""
echo "🔒 SSL证书配置提醒:"
echo "================================="
echo "当前配置需要SSL证书，请选择以下方案之一："
echo ""
echo "方案1: 使用Let's Encrypt免费证书 (推荐)"
echo "   安装certbot: apt install certbot python3-certbot-nginx"
echo "   获取证书: certbot --nginx -d aikline.haidian666.com"
echo ""
echo "方案2: 使用自签名证书 (仅测试用)"
echo "   运行: ./generate_ssl.sh"
echo ""
echo "方案3: 仅使用HTTP (不推荐)"
echo "   编辑配置文件，启用HTTP配置部分"

# 检查nginx配置语法
echo ""
echo "🔍 检查nginx配置语法..."
if nginx -t; then
    echo "✅ nginx配置语法正确"
    
    # 询问是否重启nginx
    read -p "🔄 是否重启nginx服务? (y/N): " restart_nginx
    if [[ $restart_nginx =~ ^[Yy]$ ]]; then
        systemctl restart nginx
        systemctl enable nginx
        echo "✅ nginx服务已重启并设置开机自启"
        
        # 显示状态
        echo ""
        echo "📊 服务状态:"
        systemctl status nginx --no-pager -l
    else
        echo "⚠️  请手动重启nginx: sudo systemctl restart nginx"
    fi
else
    echo "❌ nginx配置有误，请检查配置文件"
    exit 1
fi

echo ""
echo "🎉 安装完成!"
echo "================================="
echo "🌐 域名: aikline.haidian666.com"
echo "🔗 本地服务: http://127.0.0.1:8018"
echo "📝 配置文件: $CONFIG_FILE"
echo "📋 日志文件: /var/log/nginx/aikline_*.log"
echo ""
echo "📋 后续步骤:"
echo "1. 确保域名解析指向此服务器IP"
echo "2. 配置SSL证书 (推荐使用Let's Encrypt)"
echo "3. 启动AI看线服务: ./service.sh start"
echo "4. 测试访问: https://aikline.haidian666.com"
echo ""
echo "🔧 管理命令:"
echo "   重启nginx: sudo systemctl restart nginx"
echo "   查看日志: sudo tail -f /var/log/nginx/aikline_*.log"
echo "   检查配置: sudo nginx -t" 