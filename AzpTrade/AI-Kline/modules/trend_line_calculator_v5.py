import numpy as np
import pandas as pd
from scipy import stats
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

class TrendLineCalculatorV5:
    """
    V5版K线趋势线计算器 - 简化版本，删除冗余代码
    
    核心功能：
    1. 智能趋势判断（上升/下降/横盘）
    2. 高效的极值点识别
    3. 支撑线和阻力线计算
    4. 根据趋势方向优化评分
    """
    
    def __init__(self):
        """
        初始化趋势线计算器
        （V5.1版：移除固定的容错率参数，改为内部动态计算）
        """
        pass
    
    def calculate_trend_lines(self, stock_data: pd.DataFrame, auto_trim: bool = False) -> Dict:
        """
        计算最重要的支撑线和阻力线
        
        参数:
            stock_data (pd.DataFrame): 包含OHLC数据的DataFrame
            auto_trim (bool): 是否自动修剪前期横盘数据，默认False
        """
        if len(stock_data) < 20:
            return {"support_line": None, "resistance_line": None, "error": "数据点不足"}
        
        # 重置索引
        data = stock_data.copy().reset_index(drop=True)
        original_length = len(data)
        
        # 自动修剪前期横盘数据
        trend_start_idx = 0
        if auto_trim and len(data) > 30:
            trend_start_idx = self._find_trend_start_point(data)
            if trend_start_idx > 0:
                print(f"🔍 检测到趋势起始点: 第{trend_start_idx}天，忽略前{trend_start_idx}天的横盘数据")
                data = data.iloc[trend_start_idx:].reset_index(drop=True)
        
        # 1. 判断整体趋势
        trend_direction = self._detect_trend(data)
        print(f"📈 检测到整体趋势: {trend_direction}")
        
        # 2. 识别高低点
        highs = self._find_extrema(data['high'].values, find_peaks=True)
        lows = self._find_extrema(data['low'].values, find_peaks=False)
        
        print(f"识别到 {len(highs)} 个高点, {len(lows)} 个低点")
        
        # 3. 计算支撑线和阻力线
        best_support = self._find_best_support_line(data, lows)
        best_resistance = self._find_best_resistance_line(data, highs)
        
        # 4. 如果进行了数据修剪，需要调整连接点索引和趋势线公式回到原始数据
        if trend_start_idx > 0:
            if best_support and 'connected_points' in best_support:
                # 调整连接点索引
                best_support['connected_points'] = [idx + trend_start_idx for idx in best_support['connected_points']]
                best_support['start_index'] += trend_start_idx
                best_support['end_index'] += trend_start_idx
                
                # 关键：调整趋势线公式的截距，使其对应原始数据索引
                slope = best_support['slope']
                old_intercept = best_support['intercept']
                # 新截距 = 旧截距 - 斜率 * 修剪的天数
                new_intercept = old_intercept - slope * trend_start_idx
                best_support['intercept'] = new_intercept
                best_support['equation'] = f"y = {slope:.4f}x + {new_intercept:.4f}"
                
            if best_resistance and 'connected_points' in best_resistance:
                # 调整连接点索引
                best_resistance['connected_points'] = [idx + trend_start_idx for idx in best_resistance['connected_points']]
                best_resistance['start_index'] += trend_start_idx
                best_resistance['end_index'] += trend_start_idx
                
                # 关键：调整趋势线公式的截距，使其对应原始数据索引
                slope = best_resistance['slope']
                old_intercept = best_resistance['intercept']
                # 新截距 = 旧截距 - 斜率 * 修剪的天数
                new_intercept = old_intercept - slope * trend_start_idx
                best_resistance['intercept'] = new_intercept
                best_resistance['equation'] = f"y = {slope:.4f}x + {new_intercept:.4f}"
        
        # 5. 根据趋势方向调整评分
        if best_support and trend_direction == "上升" and best_support.get('slope', 0) > 0:
            best_support['quality_score'] += 15  # 上升趋势中的上升支撑线加分
        if best_resistance and trend_direction == "下降" and best_resistance.get('slope', 0) < 0:
            best_resistance['quality_score'] += 15  # 下降趋势中的下降阻力线加分
        
        return {
            "support_line": best_support,
            "resistance_line": best_resistance,
            "total_highs": len(highs),
            "total_lows": len(lows),
            "analysis_period": len(data),
            "original_period": original_length,
            "trend_start_index": trend_start_idx,
            "trimmed_data": trend_start_idx > 0,
            "trend_direction": trend_direction
        }
    
    def _find_trend_start_point(self, data: pd.DataFrame) -> int:
        """
        寻找趋势起始点，忽略前期横盘数据
        """
        if len(data) < 30:
            return 0
        
        # 方法1: 基于价格突破检测
        breakthrough_point = self._detect_price_breakthrough(data)
        
        # 方法2: 基于波动性变化检测  
        volatility_point = self._detect_volatility_change(data)
        
        # 方法3: 基于趋势强度检测
        trend_strength_point = self._detect_trend_strength_change(data)
        
        # 综合判断，选择最合理的起始点
        candidates = [breakthrough_point, volatility_point, trend_strength_point]
        candidates = [p for p in candidates if p > 0 and p < len(data) // 2]  # 过滤无效值，不超过50%
        
        if not candidates:
            return 0
        
        # 选择中位数作为起始点，避免过于激进或保守
        candidates.sort()
        median_point = candidates[len(candidates) // 2]
        
        # 不要修剪超过30%的数据
        max_trim = len(data) // 3
        return min(median_point, max_trim)
    
    def _detect_price_breakthrough(self, data: pd.DataFrame) -> int:
        """检测价格突破点"""
        if len(data) < 20:
            return 0
        
        # 计算前期价格区间（前25%数据）
        early_period = min(20, len(data) // 4)
        early_high = data['high'][:early_period].max()
        early_low = data['low'][:early_period].min()
        early_range = early_high - early_low
        early_mid = (early_high + early_low) / 2
        
        # 如果前期波动很小（横盘），寻找突破点
        if early_range < early_mid * 0.15:  # 前期波动小于中间价的15%
            # 寻找价格突破早期区间的点
            for i in range(early_period, len(data)):
                high_breakthrough = data['high'].iloc[i] > early_high * 1.08  # 向上突破8%
                low_breakthrough = data['low'].iloc[i] < early_low * 0.92   # 向下突破8%
                
                if high_breakthrough or low_breakthrough:
                    return max(0, i - 5)  # 往前推5天作为起始点
        
        return 0
    
    def _detect_volatility_change(self, data: pd.DataFrame) -> int:
        """检测波动性变化点"""
        if len(data) < 25:
            return 0
        
        # 计算滚动波动率
        window = 8
        volatilities = []
        
        for i in range(window, len(data)):
            period_data = data['close'].iloc[i-window:i]
            volatility = period_data.std() / period_data.mean()
            volatilities.append(volatility)
        
        if len(volatilities) < 15:
            return 0
        
        # 寻找波动率显著增加的点
        early_vol = np.mean(volatilities[:8])  # 前8个窗口的平均波动率
        
        for i in range(8, len(volatilities)):
            current_vol = np.mean(volatilities[i-3:i+1])  # 4窗口平均波动率
            if current_vol > early_vol * 1.8:  # 波动率增加80%以上
                return window + i - 3
        
        return 0
    
    def _detect_trend_strength_change(self, data: pd.DataFrame) -> int:
        """检测趋势强度变化点"""
        if len(data) < 20:
            return 0
        
        # 计算价格动量
        window = 5
        momentums = []
        
        for i in range(window, len(data)):
            price_change = abs(data['close'].iloc[i] - data['close'].iloc[i-window]) / data['close'].iloc[i-window]
            momentums.append(price_change)
        
        if len(momentums) < 15:
            return 0
        
        # 寻找动量显著增加的点
        early_momentum = np.mean(momentums[:8])
        
        for i in range(8, len(momentums)):
            current_momentum = np.mean(momentums[i-3:i+1])
            if current_momentum > early_momentum * 2.5:  # 动量增加150%以上
                return window + i - 3
        
        return 0
    
    def _detect_trend(self, data: pd.DataFrame) -> str:
        """
        V5.3: 检测整体趋势方向（增强版，更注重近期趋势）
        """
        n = len(data)
        if n < 20:
            return "横盘"
        
        close_prices = data['close'].values
        x = np.arange(n)

        # 1. 整体趋势 (基于所有数据)
        overall_slope, _, _, _, _ = stats.linregress(x, close_prices)
        
        # 2. 近期趋势 (基于后半段数据)
        last_half_n = n // 2
        recent_prices = close_prices[-last_half_n:]
        recent_x = np.arange(last_half_n)
        recent_slope, _, _, _, _ = stats.linregress(recent_x, recent_prices)
        
        # 3. 价格水平比较 (后1/3 vs 前1/3)
        third_n = n // 3
        first_third_avg = np.mean(close_prices[:third_n])
        last_third_avg = np.mean(close_prices[-third_n:])
        price_change_pct = (last_third_avg - first_third_avg) / first_third_avg

        print(f"趋势分析: 整体斜率={overall_slope:.4f}, 近期斜率={recent_slope:.4f}, 价格变化={price_change_pct:.1%}")

        # 决策逻辑：近期趋势优先
        # 将斜率与平均价格进行比较，以实现标准化
        avg_price = np.mean(close_prices)
        
        # 如果近期斜率明确...
        if recent_slope / avg_price > 0.002:  # 近期显著上涨
            return "上升"
        if recent_slope / avg_price < -0.002: # 近期显著下跌
            return "下降"
            
        # 如果近期趋势不明确，参考整体趋势和价格水平变化
        if overall_slope / avg_price > 0.001 and price_change_pct > 0.05:
            return "上升"
        if overall_slope / avg_price < -0.001 and price_change_pct < -0.05:
            return "下降"
            
        return "横盘"
    
    def _find_extrema(self, prices: np.ndarray, find_peaks: bool = True) -> List[int]:
        """
        识别极值点（改进版本，专注于真正重要的关键点位）
        """
        extrema = []
        n = len(prices)
        
        # 对于低点，使用多层次策略，平衡全局和局部重要性
        if not find_peaks:
            # 1. 强制包含全局最低点
            global_min_idx = np.argmin(prices)
            global_min_price = prices[global_min_idx]
            extrema.append(global_min_idx)
            
            # 2. 包含接近全局最低价的点
            for i in range(n):
                if abs(prices[i] - global_min_price) < global_min_price * 0.02:  # 稍微放宽到2%
                    if i not in extrema:
                        extrema.append(i)
            
            # 3. 分层次寻找重要低点 - 确保不遗漏关键支撑位
            # 使用多个价格层次，确保覆盖所有重要低点
            price_thresholds = [1.02, 1.05, 1.08, 1.12, 1.16]  # 增加更多层次
            
            for threshold in price_thresholds:
                price_limit = global_min_price * threshold
                threshold_points = []
                
                # 寻找该价格范围内的局部低点
                for i in range(2, n - 2):  # 适中的边界要求
                    if prices[i] <= price_limit and i not in extrema:
                        # 使用灵活的局部低点判断 - 不要过于严格
                        windows = [1, 2, 3]  # 多种窗口大小
                        is_local_low = False
                        
                        for window in windows:
                            temp_is_low = True
                            lower_count = 0
                            # 允许少量点比当前点更低，提高容错性
                            for j in range(max(0, i-window), min(n, i+window+1)):
                                if j != i and prices[j] < prices[i]:
                                    lower_count += 1
                            
                            # 如果窗口内低于当前点的数量不超过1个，认为是局部低点
                            if lower_count <= 1:
                                is_local_low = True
                                break
                        
                        if is_local_low:
                            threshold_points.append(i)
                
                # 对于每个阈值，选择最重要的点（按价格排序，选择更低的）
                if threshold_points:
                    # 按价格排序，优先选择价格更低的点
                    threshold_points.sort(key=lambda x: prices[x])
                    
                    # 根据阈值选择不同数量的点
                    if threshold <= 1.05:
                        max_points = 6  # 最接近全局最低点的区间，要更多点
                    elif threshold <= 1.10:
                        max_points = 4  # 中等区间
                    else:
                        max_points = 2  # 较高区间，只要最重要的点
                    
                    for point in threshold_points[:max_points]:
                        if point not in extrema:
                            extrema.append(point)
                
                # 总数控制，但不要过于严格
                if len(extrema) >= 18:  # 适当增加总数限制
                    break
        
        # 4. 加强的局部极值检测 - 专门用于补充重要但被遗漏的点
        if not find_peaks:  # 只对低点使用这个增强检测
            # 使用多种窗口大小进行扫描，确保不遗漏任何重要低点
            windows = [3, 5, 7, 10, 15]  # 更大的窗口范围
            
            for window in windows:
                for i in range(window, n - window):
                    if i in extrema:  # 已经是极值点，跳过
                        continue
                        
                    current_price = prices[i]
                    
                    # 检查这个点是否在较大窗口内足够低
                    window_prices = prices[max(0, i-window):min(n, i+window+1)]
                    window_min = np.min(window_prices)
                    
                    # 如果当前点是窗口内最低点，或者与最低点非常接近
                    if (current_price == window_min or 
                        abs(current_price - window_min) < window_min * 0.01):
                        
                        # 进一步验证：这个点是否有局部重要性
                        # 检查前后至少3个点是否都不低于当前点
                        is_local_important = True
                        check_range = min(3, window//2)
                        
                        for j in range(max(0, i-check_range), min(n, i+check_range+1)):
                            if j != i and prices[j] < current_price * 0.995:  # 允许0.5%误差
                                is_local_important = False
                                break
                        
                        if is_local_important:
                            extrema.append(i)
        
        # 通用的滑动窗口法（适用于高点和低点的补充）
        windows = [1, 2, 3, 5] if n <= 100 else [2, 3, 5, 8]
        
        for window in windows:
            for i in range(window, n - window):
                current_price = prices[i]
                is_extremum = True
                
                # 检查窗口内是否为极值
                for j in range(i - window, i + window + 1):
                    if j == i:
                        continue
                    if find_peaks:
                        if current_price <= prices[j]:
                            is_extremum = False
                            break
                    else:
                        if current_price >= prices[j]:
                            is_extremum = False
                            break
                
                if is_extremum and i not in extrema:
                    extrema.append(i)
        
        # 简单相邻比较法作为补充
        for i in range(1, n - 1):
            if find_peaks:
                if prices[i] > prices[i-1] and prices[i] > prices[i+1] and i not in extrema:
                    extrema.append(i)
            else:
                if prices[i] < prices[i-1] and prices[i] < prices[i+1] and i not in extrema:
                    extrema.append(i)
        
        # 基于价格百分位数的方法
        if find_peaks:
            # 对于高点，找前20%的价格
            price_threshold = np.percentile(prices, 80)
            for i in range(n):
                if prices[i] >= price_threshold and i not in extrema:
                    extrema.append(i)
        else:
            # 对于低点，收紧识别范围：找后20%的价格
            # 避免引入过多非关键点
            price_threshold = np.percentile(prices, 20)  # 从40收紧到20
            for i in range(n):
                if prices[i] <= price_threshold and i not in extrema:
                    extrema.append(i)
        
        # 去重并排序
        extrema = sorted(list(set(extrema)))
        
        # 对于低点，使用适中的合并策略，避免过度合并关键低点
        if find_peaks:
            min_distance = max(1, n // 15)  # 高点合并距离
        else:
            min_distance = max(1, n // 20)  # 低点使用适中的距离，避免过度合并
        
        extrema = self._merge_close_points(extrema, prices, find_peaks, min_distance)
        
        return extrema
    
    def _merge_close_points(self, extrema: List[int], prices: np.ndarray, 
                           find_peaks: bool, min_distance: int) -> List[int]:
        """
        合并距离太近的极值点
        """
        if len(extrema) <= 1:
            return extrema
        
        merged = [extrema[0]]
        
        for current_idx in extrema[1:]:
            if current_idx - merged[-1] >= min_distance:
                merged.append(current_idx)
            else:
                # 保留更极端的点
                prev_idx = merged[-1]
                if find_peaks:
                    if prices[current_idx] > prices[prev_idx]:
                        merged[-1] = current_idx
                else:
                    if prices[current_idx] < prices[prev_idx]:
                        merged[-1] = current_idx
        
        return merged
    
    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """
        计算平均真实波幅 (ATR)
        """
        if len(data) < period:
            return data['high'].mean() - data['low'].mean() # 如果数据不足，返回平均日波幅

        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = tr.ewm(alpha=1/period, adjust=False).mean().iloc[-1]
        
        return atr if atr > 0 else np.mean(tr)

    def _get_point_weights(self, data: pd.DataFrame, extrema_indices: List[int], line_type: str) -> List[Dict]:
        """
        为每个极值点分配权重，用于RANSAC采样（统一版）
        """
        points = []
        data_length = len(data)
        trend_direction = self._detect_trend(data)
        price_column = 'low' if line_type == 'support' else 'high'
        
        all_prices = [data[price_column].iloc[idx] for idx in extrema_indices]
        
        if line_type == 'support':
            # --- 支撑线权重逻辑（优化版） ---
            min_price = min(all_prices)
            max_price = max(all_prices)
            price_range = max_price - min_price
            
            # 扩大关键支撑位的范围：最低价+12%以内（原来是6%）
            key_price_threshold = min_price * 1.12
            
            # 增加中等重要支撑位：最低价+25%以内
            mid_price_threshold = min_price * 1.25
            
            for idx in extrema_indices:
                price = data[price_column].iloc[idx]
                
                # 价格权重（更精细的分层）
                if price <= key_price_threshold:
                    # 关键支撑区域 - 高权重
                    price_ratio = (price - min_price) / (key_price_threshold - min_price)
                    price_weight = 5.0 * (1.0 - price_ratio) + 2.0  # 2.0-7.0
                elif price <= mid_price_threshold:
                    # 中等支撑区域 - 中等权重  
                    price_ratio = (price - key_price_threshold) / (mid_price_threshold - key_price_threshold)
                    price_weight = 2.0 * (1.0 - price_ratio) + 1.0  # 1.0-2.0
                else:
                    # 较高区域 - 低权重但不忽略
                    distance_factor = min(3.0, (price - mid_price_threshold) / price_range)
                    price_weight = max(0.3, 1.0 - distance_factor * 0.3)  # 0.3-1.0
                
                # 时间权重 - 支撑线适度偏重近期点
                time_weight = 1.0 + (idx / data_length) * 0.3
                
                # 全局最低点特殊奖励
                if abs(price - min_price) < min_price * 0.005: 
                    price_weight *= 10.0  # 最低点超高权重
                elif abs(price - min_price) < min_price * 0.015: 
                    price_weight *= 6.0   # 接近最低点高权重
                elif abs(price - min_price) < min_price * 0.04: 
                    price_weight *= 3.0   # 相对较低点中等权重
                
                # 局部最低点奖励 - 识别区间内的重要低点
                local_window = min(20, len(extrema_indices) // 3)
                if local_window > 2:
                    local_start = max(0, idx - local_window)
                    local_end = min(data_length, idx + local_window)
                    local_min = data[price_column].iloc[local_start:local_end].min()
                    
                    if abs(price - local_min) < local_min * 0.02:  # 是局部最低点
                        price_weight *= 2.0
                
                final_weight = price_weight * time_weight
                points.append({'index': idx, 'price': price, 'weight': final_weight})

        else:  # line_type == 'resistance'
            # --- 阻力线权重逻辑（V5.3: 修正bug并保持核心逻辑） ---
            all_prices_list = [data[price_column].iloc[i] for i in extrema_indices]
            max_price = max(all_prices_list)
            global_max_idx = extrema_indices[all_prices_list.index(max_price)]
            
            for idx in extrema_indices:
                price = data[price_column].iloc[idx]
                
                # 价格权重
                price_weight = 1.0 + ((price - min(all_prices_list)) / (max_price - min(all_prices_list)) if (max_price - min(all_prices_list)) > 0 else 0) # 1.0 - 2.0
                    
                # 时间权重 (V5.2: 极度强调近期点)
                time_weight = 1.0 + (idx / data_length) * 2.0 # 1.0 - 3.0
                
                # V5.2 核心改动：给予全局最高点巨大的、压倒性的权重
                if idx == global_max_idx:
                    price_weight *= 50.0  # 全局最高点拥有绝对优先权
                
                final_weight = price_weight * time_weight
                points.append({'index': idx, 'price': price, 'weight': final_weight, 'is_global_max': idx == global_max_idx})
                
        return points

    def _run_ransac_fitter(self, data: pd.DataFrame, points: List[Dict], 
                           trend_direction: str, line_type: str) -> Optional[Dict]:
        """
        统一的RANSAC执行器（集成动态容错率）
        """
        if len(points) < 2:
            return None
        
        import random
        random.seed(42)
        
        best_line = None
        best_score = -float('inf')
        max_iterations = min(500, len(points) * 50) # 增加迭代次数以探索更多可能
        
        # --- 核心改进：动态容错率 ---
        atr = self._calculate_atr(data)
        avg_price = np.mean([p['price'] for p in points])
        # 将ATR转换为百分比，基础容错为ATR的60%，但限制在2%-7%之间
        tolerance = np.clip((atr / avg_price) * 0.6, 0.02, 0.07)
        
        # 识别关键点 (扩大关键点选择范围)
        all_prices = sorted([p['price'] for p in points])
        if line_type == 'support':
            # 支撑线：选择价格最低25%的点作为关键点（原来是10%）
            key_threshold_idx = min(len(all_prices)-1, len(all_prices) // 4)
            key_price_threshold = all_prices[key_threshold_idx]
            key_points = [p for p in points if p['price'] <= key_price_threshold]
            
            # 额外增加全局最低点周围的点
            min_price = all_prices[0]
            nearby_min_points = [p for p in points if abs(p['price'] - min_price) < min_price * 0.05]
            
            # 合并关键点，去重
            key_points_set = set(p['index'] for p in key_points)
            for p in nearby_min_points:
                if p['index'] not in key_points_set:
                    key_points.append(p)
                    key_points_set.add(p['index'])
        else: # resistance
            key_price_threshold = all_prices[max(0, len(all_prices) - 1 - len(all_prices) // 10)]
            key_points = [p for p in points if p['price'] >= key_price_threshold]
            
        # RANSAC迭代
        for _ in range(max_iterations):
            # V5.2 核心改动：对于阻力线，强制从全局最高点开始采样
            if line_type == 'resistance':
                global_max_point_list = [p for p in points if p.get('is_global_max', False)]
                if global_max_point_list and random.random() < 0.8: # 80%的概率强制使用全局最高点
                    p1 = global_max_point_list[0]
                    p2 = random.choice(points)
                else: # 保持一定的随机性
                    if len(points) < 2: continue
                    p1, p2 = random.sample(points, 2)
            else: # 支撑线逻辑保持不变
                if len(key_points) >= 1 and random.random() < 0.85:
                    p1 = random.choice(key_points)
                    p2 = random.choice(points)
                else:
                    if len(points) < 2: continue
                    p1, p2 = random.sample(points, 2)

            if p1['index'] == p2['index']: continue

            # --- 核心改动：根据线条类型调用不同的评估函数 ---
            if line_type == 'support':
                line_result = self._evaluate_support_line(
                    p1, p2, points, tolerance, data
                )
            else: # resistance
                line_result = self._evaluate_resistance_line(
                    p1, p2, points, tolerance, trend_direction, data
                )
            
            if line_result and line_result['score'] > best_score:
                best_score = line_result['score']
                best_line = line_result
                
        return best_line

    def _find_best_support_line(self, data: pd.DataFrame, lows: List[int]) -> Optional[Dict]:
        """
        寻找最佳支撑线（V5.4: 保持RANSAC框架，但使用更优的评估函数）
        """
        if len(lows) < 2:
            return None
        
        print(f"  🔍 使用【V5.4 支撑线专用RANSAC算法】寻找支撑线，候选点：{len(lows)}个")
        
        points = self._get_point_weights(data, lows, 'support')
        trend_direction = self._detect_trend(data)
        
        best_line = self._run_ransac_fitter(data, points, trend_direction, 'support')
        
        if best_line:
            print(f"    ✅ RANSAC找到支撑线: 斜率{best_line['slope']:.4f}, 连接{best_line['point_count']}点, 得分{best_line['score']:.1f}")
            return self._enhance_line_info(data, best_line, 'support')
        return None

    def _find_best_resistance_line(self, data: pd.DataFrame, highs: List[int]) -> Optional[Dict]:
        """
        V7: 寻找最佳阻力线，锚定全局最高点
        """
        if len(highs) < 2:
            return None

        print(f"  🔍 使用【V7 全局最高点锚定】算法寻找阻力线...")

        # 1. 找到全局最高点
        high_prices = data['high'].iloc[highs]
        global_max_idx = high_prices.idxmax()
        global_max_price = high_prices.max()
        
        best_line = None
        best_score = -float('inf')

        # 2. 遍历其他所有高点作为第二个锚点
        other_highs = [h for h in highs if h != global_max_idx]

        for p_other_idx in other_highs:
            p_max = (global_max_idx, global_max_price)
            p_other = (p_other_idx, data['high'].iloc[p_other_idx])
            
            if p_max[0] == p_other[0]: continue
            
            slope = (p_other[1] - p_max[1]) / (p_other[0] - p_max[0])
            if slope > 0.01: # 允许轻微斜率向上，但不鼓励
                continue

            intercept = p_max[1] - slope * p_max[0]

            # 3. 评估这条线: 计算连接点
            connected_points_indices = []
            tolerance = 0.02 # 2% tolerance
            for h_idx in highs:
                expected_price = slope * h_idx + intercept
                if abs(expected_price - data['high'].iloc[h_idx]) / data['high'].iloc[h_idx] < tolerance:
                    connected_points_indices.append(h_idx)
            
            # 4. 计算分数
            point_count = len(connected_points_indices)
            if point_count < 2:
                continue

            score = 0
            # a. 连接点数量是核心
            score += point_count * 25

            # b. 奖励连接到近期点的线
            # 找到连接点中最靠右（最近）的点
            most_recent_connection_idx = max(connected_points_indices)
            score += (most_recent_connection_idx / len(data)) * 20

            # c. 对穿透K线的行为进行惩罚
            violation_count = 0
            for i in range(len(data)):
                if data['high'].iloc[i] > (slope * i + intercept) * 1.01:
                    violation_count += 1
            
            violation_penalty = violation_count * 2
            score -= violation_penalty

            if score > best_score:
                best_score = score
                best_line = {
                    'slope': slope, 'intercept': intercept, 'connected_points': sorted(list(set(connected_points_indices))),
                    'point_count': point_count, 'quality_score': score, 'score': score,
                    'line_type': 'resistance', 'equation': f"y = {slope:.4f}x + {intercept:.4f}",
                    'start_index': min(connected_points_indices), 'end_index': max(connected_points_indices),
                    'start_price': data['high'].iloc[min(connected_points_indices)],
                    'end_price': data['high'].iloc[max(connected_points_indices)],
                }
    
        if best_line:
            print(f"    ✅ V7找到阻力线: 斜率{best_line['slope']:.4f}, 连接{best_line['point_count']}点, 得分{best_line['score']:.1f}")
            return self._enhance_line_info(data, best_line, 'resistance')
        
        print("    ❌ V7未能找到合适的阻力线")
        return None
        
    def _is_meaningful_peak(self, index, prices, window=10):
        """判断一个点是否是其局部的极值点"""
        n = len(prices)
        start = max(0, index - window)
        end = min(n, index + window + 1)
        return prices[index] == np.max(prices[start:end])
    
    def _evaluate_resistance_line(self, p1: Dict, p2: Dict, all_points: List[Dict],
                                  tolerance: float, trend_direction: str, data: pd.DataFrame
                                 ) -> Optional[Dict]:
        """
        [此函数已废弃] - 阻力线计算已由 _find_best_resistance_line 的 V7 版本取代
        """
        return None
        
    def _evaluate_support_line(self, p1: Dict, p2: Dict, all_points: List[Dict],
                               tolerance: float, data: pd.DataFrame) -> Optional[Dict]:
        """
        评估【支撑线】候选者的质量 - V5.4 优化版
        """
        x1, y1 = p1['index'], p1['price']
        x2, y2 = p2['index'], p2['price']
        
        if x2 == x1: return None
            
        slope = (y2 - y1) / (x2 - x1)
        intercept = y1 - slope * x1
        
        violation_count = 0
        for i in range(len(data)):
            if data['low'].iloc[i] < (slope * i + intercept) * 0.995: # 0.5% 容忍度
                violation_count += 1
        
        if violation_count > 3: # 最多只允许3个交易日的低点穿透
            return None
            
        connected_points_indices = []
        for point in all_points:
            expected_price = slope * point['index'] + intercept
            if abs(point['price'] - expected_price) / point['price'] <= tolerance * 1.5:
                connected_points_indices.append(point['index'])

        if len(connected_points_indices) < 2: return None
        
        score = 0
        point_count = len(connected_points_indices)
        score += (point_count ** 3) * 2 # 点数最重要: 2点=16, 3点=54, 4点=128
        
        all_prices = sorted([p['price'] for p in all_points])
        key_zone_threshold = all_prices[min(len(all_prices)-1, len(all_prices) // 4)]
        
        connected_prices = [data['low'].iloc[idx] for idx in connected_points_indices]
        num_in_key_zone = sum(1 for p in connected_prices if p <= key_zone_threshold)
        
        score += num_in_key_zone * 10 # 奖励连接关键区域的点
        score += min(20, (max(connected_points_indices) - min(connected_points_indices)) / 5) # 时间跨度

        global_min_price = all_prices[0]
        if any(abs(p - global_min_price) < global_min_price * 0.01 for p in connected_prices):
            score += 15 # 连接到全局最低点是加分项，但不是决定性因素
            
        return {
            'slope': slope, 'intercept': intercept, 'connected_points': connected_points_indices,
            'point_count': point_count, 'quality_score': score, 'score': score,
            'line_type': 'support', 'equation': f"y = {slope:.4f}x + {intercept:.4f}",
            'start_index': min(connected_points_indices), 'end_index': max(connected_points_indices),
            'start_price': data['low'].iloc[min(connected_points_indices)],
            'end_price': data['low'].iloc[max(connected_points_indices)],
        }
        
    def _evaluate_line_candidate(self, p1: Dict, p2: Dict, all_points: List[Dict],
                                tolerance: float, trend_direction: str, data: pd.DataFrame,
                                line_type: str) -> Optional[Dict]:
        """
        [此函数已废弃] - 逻辑已被拆分到 _evaluate_support_line 和 _find_best_resistance_line
        """
        return None
    
    def _is_valid_trend_line(self, slope: float, trend_direction: str, line_type: str) -> bool:
        """
        检查趋势线是否符合趋势约束
        """
        # 基本斜率合理性检查 - 重点是下调支撑线，斜率限制保持温和
        if abs(slope) > 1.5:  # 每天变化超过1.5元，太陡峭
            return False
        
        if line_type == 'resistance':
            if trend_direction == "上升":
                # 上升趋势的阻力线：应该有明显上升趋势，避免完全水平
                return 0.02 <= slope <= 1.2  # 最小斜率0.02
            elif trend_direction == "下降":
                # 下降趋势的阻力线：可以是下降的
                return -0.8 <= slope <= -0.02
            else:
                # 横盘趋势：允许水平阻力线
                return abs(slope) <= 0.05
        
        else:  # support
            if trend_direction == "上升":
                # 上升趋势的支撑线：必须有明显的上升趋势，避免水平线
                return 0.02 <= slope <= 1.2  # 最小斜率0.02，确保有明显趋势
            elif trend_direction == "下降":
                # 下降趋势的支撑线：可以是下降的，但不能太陡
                return -0.8 <= slope <= 0.02
            else:
                # 横盘趋势：允许更大的斜率范围，连接重要低点
                # 原来只允许接近水平的线，现在允许有一定斜度
                return abs(slope) <= 0.15  # 从0.05放宽到0.15，允许连接关键支撑位
    
    def _calculate_ransac_score(self, support_points: List[int], all_points: List[Dict],
                               weighted_error: float, total_weight: float,
                               trend_direction: str, line_type: str) -> float:
        """
        计算RANSAC线的综合评分
        """
        if total_weight == 0:
            return 0
        
        score = 0
        
        # 1. 连接点数量评分 (40分)
        point_score = min(40, len(support_points) * 8)
        score += point_score
        
        # 2. 拟合质量评分 (30分) - 加权误差越小越好
        avg_weighted_error = weighted_error / total_weight
        fit_score = max(0, 30 * (1 - avg_weighted_error / 0.1))  # 10%误差对应0分
        score += fit_score
        
        # 3. 时间跨度评分 (20分)
        time_span = max(support_points) - min(support_points)
        span_score = min(20, time_span / 5)  # 每5天1分
        score += span_score
        
        # 4. 趋势一致性奖励 (10分) - 奖励有明显趋势的线
        # 斜率越明显，得分越高（避免水平线）
        slope_bonus = 0
        if len(support_points) >= 2:
            # 为了计算斜率，这里简化处理
            # 实际的斜率验证已经在外层函数处理了
            pass  # 斜率检查在_is_valid_trend_line中处理
        
        # 5. 近期点位奖励
        data_length = len(all_points) if all_points else 60
        recent_points = [p for p in support_points if p >= data_length * 0.7]
        if recent_points:
            score += len(recent_points) * 5
        
        return score
    
    def _evaluate_trend_line(self, data: pd.DataFrame, extrema_indices: List[int], 
                           slope: float, intercept: float, price_column: str, 
                           line_type: str, start_idx: int, end_idx: int) -> Optional[Dict]:
        """
        评估趋势线质量
        """
        connected_points = self._find_connected_points(
            data, extrema_indices, slope, intercept, price_column
        )
        
        if len(connected_points) < 2:
            return None
        
        score = 0.0
        
        # 连接点数量 (40分)
        point_score = min(40, len(connected_points) * 8)
        score += point_score
        
        # 时间跨度 (30分)
        time_span = end_idx - start_idx
        span_ratio = time_span / (len(data) - 1)
        span_score = span_ratio * 30
        score += span_score
        
        # 线性拟合度 (30分)
        if len(connected_points) >= 2:
            x_vals = connected_points
            y_vals = [data[price_column].iloc[idx] for idx in connected_points]
            expected_vals = [slope * x + intercept for x in x_vals]
            
            try:
                corr = stats.pearsonr(y_vals, expected_vals)[0]
                r_squared = corr ** 2 if not np.isnan(corr) else 0
                fit_score = r_squared * 30
                score += fit_score
            except:
                pass
        
        return {
            'slope': slope,
            'intercept': intercept,
            'connected_points': connected_points,
            'point_count': len(connected_points),
            'start_index': start_idx,
            'end_index': end_idx,
            'start_price': data[price_column].iloc[start_idx],
            'end_price': data[price_column].iloc[end_idx],
            'quality_score': score,
            'line_type': line_type,
            'equation': f"y = {slope:.4f}x + {intercept:.4f}"
        }
    
    def _find_connected_points(self, data: pd.DataFrame, extrema_indices: List[int], 
                              slope: float, intercept: float, price_column: str) -> List[int]:
        """
        找到连接到趋势线的所有点
        """
        connected_points = []
        
        # 根据价格列类型选择不同的容错率
        tolerance = self.support_tolerance if price_column == 'low' else self.resistance_tolerance
        
        for idx in extrema_indices:
            actual_price = data[price_column].iloc[idx]
            expected_price = slope * idx + intercept
            
            if actual_price > 0:
                deviation = abs(actual_price - expected_price) / actual_price
                if deviation <= tolerance:
                    connected_points.append(idx)
        
        return sorted(connected_points)
    
    def _enhance_line_info(self, data: pd.DataFrame, line: Dict, line_type: str) -> Dict:
        """
        增强趋势线信息
        """
        # 趋势方向
        if line['slope'] > 0.001:
            line['trend_direction'] = '上升'
        elif line['slope'] < -0.001:
            line['trend_direction'] = '下降'
        else:
            line['trend_direction'] = '横盘'
        
        # 当前价格与趋势线的关系
        latest_index = len(data) - 1
        latest_price = data['close'].iloc[latest_index]
        expected_price = line['slope'] * latest_index + line['intercept']
        
        distance_percent = (latest_price - expected_price) / latest_price * 100
        line['current_distance_percent'] = distance_percent
        
        # 状态描述
        if line_type == 'support':
            if distance_percent > 3:
                line['current_status'] = '价格在支撑线上方'
            elif distance_percent < -3:
                line['current_status'] = '跌破支撑线'
            else:
                line['current_status'] = '接近支撑线'
        else:
            if distance_percent < -3:
                line['current_status'] = '价格在阻力线下方'
            elif distance_percent > 3:
                line['current_status'] = '突破阻力线'
            else:
                line['current_status'] = '接近阻力线'
        
        # 强度等级
        if line['quality_score'] >= 80:
            line['strength_level'] = '极强'
        elif line['quality_score'] >= 60:
            line['strength_level'] = '强'
        elif line['quality_score'] >= 40:
            line['strength_level'] = '中等'
        else:
            line['strength_level'] = '弱'
        
        return line
    
    def get_trend_line_values(self, trend_line: Dict, data_length: int) -> Tuple[List[float], List[int]]:
        """
        获取趋势线在整个数据范围内的Y值
        """
        x_values = list(range(data_length))
        y_values = [trend_line['slope'] * x + trend_line['intercept'] for x in x_values]
        
        return y_values, x_values 