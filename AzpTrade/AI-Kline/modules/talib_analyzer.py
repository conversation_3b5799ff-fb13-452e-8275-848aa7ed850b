import talib
import pandas as pd
import numpy as np

class TALibAnalyzer:
    """
    使用TA-Lib库进行技术分析的类
    提供更专业和准确的技术指标计算
    """
    
    def __init__(self):
        pass
    
    def calculate_indicators(self, stock_data):
        """
        使用TA-Lib计算各种技术指标
        
        参数:
            stock_data (pandas.DataFrame): 股票历史数据，包含high、low、close、volume列
            
        返回:
            dict: 包含各种技术指标的字典
        """
        if stock_data.empty or len(stock_data) < 30:
            return {}
        
        # 转换为numpy数组，ta-lib需要numpy数组作为输入
        high = stock_data['high'].values.astype(float)
        low = stock_data['low'].values.astype(float)
        close = stock_data['close'].values.astype(float)
        volume = stock_data['volume'].values.astype(float)
        
        # 创建结果字典
        indicators = {}
        
        # 移动平均线系列
        indicators['SMA5'] = talib.SMA(close, timeperiod=5)
        indicators['SMA10'] = talib.SMA(close, timeperiod=10)
        indicators['SMA20'] = talib.SMA(close, timeperiod=20)
        indicators['SMA30'] = talib.SMA(close, timeperiod=30)
        indicators['SMA60'] = talib.SMA(close, timeperiod=60)
        
        # 指数移动平均线
        indicators['EMA12'] = talib.EMA(close, timeperiod=12)
        indicators['EMA26'] = talib.EMA(close, timeperiod=26)
        
        # MACD指标
        macd, macd_signal, macd_hist = talib.MACD(close, 
                                                  fastperiod=12, 
                                                  slowperiod=26, 
                                                  signalperiod=9)
        indicators['MACD'] = macd
        indicators['MACD_signal'] = macd_signal
        indicators['MACD_hist'] = macd_hist
        
        # RSI指标
        indicators['RSI6'] = talib.RSI(close, timeperiod=6)
        indicators['RSI12'] = talib.RSI(close, timeperiod=12)
        indicators['RSI24'] = talib.RSI(close, timeperiod=24)
        
        # 布林带
        boll_upper, boll_middle, boll_lower = talib.BBANDS(close, 
                                                           timeperiod=20, 
                                                           nbdevup=2, 
                                                           nbdevdn=2, 
                                                           matype=0)
        indicators['BOLL_upper'] = boll_upper
        indicators['BOLL_middle'] = boll_middle
        indicators['BOLL_lower'] = boll_lower
        
        # KDJ指标（使用STOCH函数）
        k_percent, d_percent = talib.STOCH(high, low, close, 
                                           fastk_period=9, 
                                           slowk_period=3, 
                                           slowk_matype=0, 
                                           slowd_period=3, 
                                           slowd_matype=0)
        j_value = 3 * k_percent - 2 * d_percent
        indicators['K'] = k_percent
        indicators['D'] = d_percent
        indicators['J'] = j_value
        
        # 威廉指标
        indicators['WILLR'] = talib.WILLR(high, low, close, timeperiod=14)
        
        # CCI指标
        indicators['CCI'] = talib.CCI(high, low, close, timeperiod=14)
        
        # ATR平均真实范围
        indicators['ATR'] = talib.ATR(high, low, close, timeperiod=14)
        
        # SAR抛物线指标
        indicators['SAR'] = talib.SAR(high, low, acceleration=0.02, maximum=0.2)
        
        # 成交量指标
        indicators['OBV'] = talib.OBV(close, volume)  # 能量潮
        indicators['AD'] = talib.AD(high, low, close, volume)  # 累积/分派线
        
        # 动量指标
        indicators['MOM'] = talib.MOM(close, timeperiod=10)  # 动量
        indicators['ROC'] = talib.ROC(close, timeperiod=10)  # 变动率
        
        # TRIX指标
        indicators['TRIX'] = talib.TRIX(close, timeperiod=14)
        
        # 转换为pandas Series以便于处理
        for key, value in indicators.items():
            if isinstance(value, np.ndarray):
                indicators[key] = pd.Series(value, index=stock_data.index)
        
        return indicators
    
    def get_pattern_recognition(self, stock_data):
        """
        使用TA-Lib进行K线形态识别
        
        参数:
            stock_data (pandas.DataFrame): 股票历史数据
            
        返回:
            dict: 包含各种K线形态识别结果的字典
        """
        if stock_data.empty or len(stock_data) < 10:
            return {}
        
        # 转换为numpy数组
        open_price = stock_data['open'].values.astype(float)
        high = stock_data['high'].values.astype(float)
        low = stock_data['low'].values.astype(float)
        close = stock_data['close'].values.astype(float)
        
        patterns = {}
        
        # 常见的K线形态
        patterns['DOJI'] = talib.CDLDOJI(open_price, high, low, close)  # 十字星
        patterns['HAMMER'] = talib.CDLHAMMER(open_price, high, low, close)  # 锤子线
        patterns['ENGULFING'] = talib.CDLENGULFING(open_price, high, low, close)  # 吞噬形态
        patterns['SHOOTING_STAR'] = talib.CDLSHOOTINGSTAR(open_price, high, low, close)  # 流星
        patterns['HANGING_MAN'] = talib.CDLHANGINGMAN(open_price, high, low, close)  # 上吊线
        patterns['MORNING_STAR'] = talib.CDLMORNINGSTAR(open_price, high, low, close)  # 晨星
        patterns['EVENING_STAR'] = talib.CDLEVENINGSTAR(open_price, high, low, close)  # 暮星
        patterns['THREE_WHITE_SOLDIERS'] = talib.CDL3WHITESOLDIERS(open_price, high, low, close)  # 三个白兵
        patterns['THREE_BLACK_CROWS'] = talib.CDL3BLACKCROWS(open_price, high, low, close)  # 三只乌鸦
        
        # 转换为pandas Series
        for key, value in patterns.items():
            if isinstance(value, np.ndarray):
                patterns[key] = pd.Series(value, index=stock_data.index)
        
        return patterns
    
    def get_support_resistance_levels(self, stock_data, window=20):
        """
        计算支撑和阻力位
        
        参数:
            stock_data (pandas.DataFrame): 股票历史数据
            window (int): 计算窗口期
            
        返回:
            dict: 包含支撑和阻力位的字典
        """
        if stock_data.empty or len(stock_data) < window * 2:
            return {}
        
        high = stock_data['high'].values.astype(float)
        low = stock_data['low'].values.astype(float)
        close = stock_data['close'].values.astype(float)
        
        # 计算支撑阻力位
        support_resistance = {}
        
        # 使用简单移动平均作为动态支撑阻力位
        support_resistance['support_sma20'] = talib.SMA(low, timeperiod=window)
        support_resistance['resistance_sma20'] = talib.SMA(high, timeperiod=window)
        
        # 使用布林带作为支撑阻力位
        boll_upper, boll_middle, boll_lower = talib.BBANDS(close, timeperiod=window)
        support_resistance['boll_support'] = boll_lower
        support_resistance['boll_resistance'] = boll_upper
        
        # 转换为pandas Series
        for key, value in support_resistance.items():
            if isinstance(value, np.ndarray):
                support_resistance[key] = pd.Series(value, index=stock_data.index)
        
        return support_resistance 