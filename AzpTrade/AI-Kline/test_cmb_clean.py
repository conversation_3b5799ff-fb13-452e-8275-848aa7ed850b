#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
招商银行(600036)改进版趋势线测试 - 只显示最重要的两条线
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from modules.data_fetcher import StockDataFetcher
from modules.trend_line_calculator_v5 import TrendLineCalculatorV5

def setup_matplotlib_chinese():
    """设置matplotlib中文显示"""
    import matplotlib.font_manager as fm
    
    # 清理字体缓存（如果需要）
    # fm._rebuild()
    
    # 设置中文字体优先级
    plt.rcParams['font.sans-serif'] = [
        'WenQuanYi Zen Hei',      # 文泉驿正黑 (Linux常用)
        'WenQuanYi Micro Hei',    # 文泉驿微米黑
        'Droid Sans Fallback',     # Android字体
        'SimHei',                  # 黑体
        'Microsoft YaHei',         # 微软雅黑
        'DejaVu Sans'              # 备用字体
    ]
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['figure.facecolor'] = 'white'
    
    # 打印当前使用的字体信息
    current_font = plt.rcParams['font.sans-serif'][0]
    print(f"📝 当前matplotlib字体设置: {current_font}")
    
    # 检查字体是否可用
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    chinese_fonts_found = []
    
    for font_name in plt.rcParams['font.sans-serif']:
        if any(font_name in available_font for available_font in available_fonts):
            chinese_fonts_found.append(font_name)
    
    if chinese_fonts_found:
        print(f"✅ 找到可用中文字体: {', '.join(chinese_fonts_found[:3])}")
    else:
        print("⚠️  未找到中文字体，可能显示为方框")
        
    return chinese_fonts_found

def plot_cmb_clean(stock_data, trend_analysis, stock_code="600036", stock_name=None, period="1年"):
    """
    绘制股票简洁的K线图和趋势线（只显示最重要的两条线）
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 14), height_ratios=[4, 1])
    
    # 获取股票名称（从参数传入）
    if stock_name is None:
        stock_name = f"股票{stock_code}"
    
    # === 绘制K线图 ===
    ax1.set_title(f'{stock_name}({stock_code}) - {period}K线图与核心趋势线', 
                 fontsize=18, fontweight='bold', pad=25)
    
    print(f"绘制K线图，共{len(stock_data)}个交易日")
    
    # 绘制蜡烛图
    for i in range(len(stock_data)):
        open_price = stock_data['open'].iloc[i]
        close_price = stock_data['close'].iloc[i]
        high_price = stock_data['high'].iloc[i]
        low_price = stock_data['low'].iloc[i]
        
        # 红涨绿跌
        color = '#FF3333' if close_price >= open_price else '#00CC00'
        
        # 绘制上下影线
        ax1.plot([i, i], [low_price, high_price], color=color, linewidth=1.2, alpha=0.8)
        
        # 绘制实体
        body_height = abs(close_price - open_price)
        if body_height > 0.01:
            ax1.plot([i, i], [open_price, close_price], color=color, linewidth=8, 
                    solid_capstyle='butt', alpha=0.9)
        else:
            # 十字星
            ax1.plot([i-0.4, i+0.4], [close_price, close_price], color=color, linewidth=3)
    
    # === 绘制支撑线（红色） ===
    trend_calculator = TrendLineCalculatorV5()
    support_line = trend_analysis['support_line']
    
    if support_line:
        print(f"找到支撑线: {support_line['equation']}")
        
        y_values, x_values = trend_calculator.get_trend_line_values(support_line, len(stock_data))
        
        # 绘制支撑线（红色）
        ax1.plot(x_values, y_values, 
                color='#FF3333',  # 红色
                linestyle='-', 
                linewidth=3, 
                alpha=0.9,
                label=f'支撑线: {support_line["point_count"]}点连接 ({support_line["strength_level"]}, {support_line["quality_score"]:.1f}分)')
        
        # 标记连接的关键点
        for point_idx in support_line['connected_points']:
            price = stock_data['low'].iloc[point_idx]
            date = stock_data['date'].iloc[point_idx]
            
            ax1.scatter(point_idx, price, 
                       color='#FF3333', 
                       s=150, 
                       marker='o', 
                       edgecolor='white', 
                       linewidth=3,
                       zorder=6)
            
            # 添加价格标注
            ax1.annotate(f'{price:.2f}', 
                        (point_idx, price), 
                        xytext=(0, -30), 
                        textcoords='offset points',
                        fontsize=10, 
                        color='#CC0000',  # 深红色
                        fontweight='bold',
                        ha='center',
                        bbox=dict(boxstyle='round,pad=0.3', 
                                facecolor='white', 
                                edgecolor='#FF3333',
                                alpha=0.9))
        
        print(f"  支撑线连接点: {support_line['connected_points']}")
    else:
        print("未找到有效的支撑线")
    
    # === 绘制阻力线（蓝色） ===
    resistance_line = trend_analysis['resistance_line']
    
    if resistance_line:
        print(f"找到阻力线: {resistance_line['equation']}")
        
        y_values, x_values = trend_calculator.get_trend_line_values(resistance_line, len(stock_data))
        
        # 绘制阻力线（蓝色）
        ax1.plot(x_values, y_values, 
                color='#4169E1',  # 皇家蓝
                linestyle='-', 
                linewidth=3, 
                alpha=0.9,
                label=f'阻力线: {resistance_line["point_count"]}点连接 ({resistance_line["strength_level"]}, {resistance_line["quality_score"]:.1f}分)')
        
        # 标记连接的关键点
        for point_idx in resistance_line['connected_points']:
            price = stock_data['high'].iloc[point_idx]
            date = stock_data['date'].iloc[point_idx]
            
            ax1.scatter(point_idx, price, 
                       color='#4169E1', 
                       s=150, 
                       marker='^', 
                       edgecolor='white', 
                       linewidth=3,
                       zorder=6)
            
            # 添加价格标注
            ax1.annotate(f'{price:.2f}', 
                        (point_idx, price), 
                        xytext=(0, 20), 
                        textcoords='offset points',
                        fontsize=10, 
                        color='#191970',  # 深蓝色
                        fontweight='bold',
                        ha='center',
                        bbox=dict(boxstyle='round,pad=0.3', 
                                facecolor='white', 
                                edgecolor='#4169E1',
                                alpha=0.9))
        
        print(f"  阻力线连接点: {resistance_line['connected_points']}")
    else:
        print("未找到有效的阻力线")
    
    # 设置x轴
    total_days = len(stock_data)
    step = max(1, total_days // 15)
    
    tick_positions = list(range(0, total_days, step))
    if tick_positions[-1] != total_days - 1:
        tick_positions.append(total_days - 1)
    
    ax1.set_xticks(tick_positions)
    ax1.set_xticklabels([stock_data['date'].iloc[i].strftime('%Y-%m-%d') 
                        for i in tick_positions], 
                       rotation=45, ha='right', fontsize=10)
    
    # 当前价格线
    current_price = stock_data['close'].iloc[-1]
    first_price = stock_data['close'].iloc[0]
    price_change = current_price - first_price
    change_percent = (price_change / first_price) * 100
    
    ax1.axhline(y=current_price, color='orange', linestyle='-', linewidth=2, alpha=0.8, 
                label=f'当前价格: {current_price:.2f} ({change_percent:+.1f}%)')
    
    # 图例和格式设置
    ax1.legend(loc='upper left', fontsize=12, frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.4, linestyle=':', linewidth=0.8)
    ax1.set_ylabel('价格 (元)', fontsize=14, fontweight='bold')
    
    # 设置价格区间
    price_min, price_max = stock_data['low'].min(), stock_data['high'].max()
    price_range = price_max - price_min
    ax1.set_ylim(price_min - price_range * 0.05, price_max + price_range * 0.12)
    
    # === 绘制成交量 ===
    ax2.set_title('成交量', fontsize=14, fontweight='bold')
    
    for i in range(len(stock_data)):
        color = '#FF3333' if stock_data['close'].iloc[i] >= stock_data['open'].iloc[i] else '#00CC00'
        ax2.bar(i, stock_data['volume'].iloc[i], color=color, width=0.8, alpha=0.7)
    
    ax2.set_xticks(tick_positions)
    ax2.set_xticklabels([stock_data['date'].iloc[i].strftime('%Y-%m-%d') 
                        for i in tick_positions], 
                       rotation=45, ha='right', fontsize=10)
    ax2.set_ylabel('成交量', fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.4, linestyle=':', linewidth=0.8)
    
    # 格式化成交量显示
    ax2.yaxis.set_major_formatter(plt.FuncFormatter(
        lambda x, p: f'{x/100000000:.1f}亿' if x >= 100000000 else f'{x/10000:.0f}万'))
    
    plt.tight_layout()
    
    # 保存图表
    # 处理文件名中的特殊字符
    if period.endswith('天'):
        period_filename = period.replace("天", "d")
    else:
        period_filename = period.replace("个月", "m").replace("1年", "1y").replace("半年", "6m")
    output_path = f'./output/charts/{stock_code}_{period_filename}_clean_trend_lines.png'
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    
    print(f"\n📊 简洁趋势线图已保存: {output_path}")
    return output_path

def check_trend_consistency(trend_analysis):
    """检查趋势一致性"""
    trend_direction = trend_analysis.get('trend_direction', '未知')
    support_line = trend_analysis.get('support_line')
    resistance_line = trend_analysis.get('resistance_line')
    
    if not support_line or not resistance_line:
        return "❓ 无法评估（缺少趋势线）"
    
    support_slope = support_line['slope']
    resistance_slope = resistance_line['slope']
    
    # 分析结果
    analysis = []
    
    if trend_direction == "上升":
        # 上升趋势：支撑线和阻力线都应该向上
        support_ok = support_slope > 0.01
        resistance_ok = resistance_slope > 0.01
        
        analysis.append(f"整体趋势: {trend_direction}")
        analysis.append(f"支撑线方向: {'✓ 上升' if support_ok else '✗ 不符合'} (斜率: {support_slope:.4f})")
        analysis.append(f"阻力线方向: {'✓ 上升' if resistance_ok else '✗ 不符合'} (斜率: {resistance_slope:.4f})")
        
        if support_ok and resistance_ok:
            return "✅ " + " | ".join(analysis) + " | 趋势一致性: 优秀"
        elif support_ok or resistance_ok:
            return "⚠️ " + " | ".join(analysis) + " | 趋势一致性: 一般"
        else:
            return "❌ " + " | ".join(analysis) + " | 趋势一致性: 差"
            
    elif trend_direction == "下降":
        # 下降趋势：支撑线和阻力线都应该向下
        support_ok = support_slope < -0.005  # 放宽支撑线要求
        resistance_ok = resistance_slope < -0.01
        
        analysis.append(f"整体趋势: {trend_direction}")
        analysis.append(f"支撑线方向: {'✓ 下降' if support_ok else '✗ 不符合'} (斜率: {support_slope:.4f})")
        analysis.append(f"阻力线方向: {'✓ 下降' if resistance_ok else '✗ 不符合'} (斜率: {resistance_slope:.4f})")
        
        if support_ok and resistance_ok:
            return "✅ " + " | ".join(analysis) + " | 趋势一致性: 优秀"
        elif support_ok or resistance_ok:
            return "⚠️ " + " | ".join(analysis) + " | 趋势一致性: 一般"
        else:
            return "❌ " + " | ".join(analysis) + " | 趋势一致性: 差"
            
    else:  # 横盘
        # 横盘趋势：支撑线和阻力线都应该接近水平
        support_ok = abs(support_slope) < 0.02
        resistance_ok = abs(resistance_slope) < 0.02
        
        analysis.append(f"整体趋势: {trend_direction}")
        analysis.append(f"支撑线方向: {'✓ 水平' if support_ok else '✗ 不符合'} (斜率: {support_slope:.4f})")
        analysis.append(f"阻力线方向: {'✓ 水平' if resistance_ok else '✗ 不符合'} (斜率: {resistance_slope:.4f})")
        
        if support_ok and resistance_ok:
            return "✅ " + " | ".join(analysis) + " | 趋势一致性: 优秀"
        elif support_ok or resistance_ok:
            return "⚠️ " + " | ".join(analysis) + " | 趋势一致性: 一般"
        else:
            return "❌ " + " | ".join(analysis) + " | 趋势一致性: 差"

def main():
    """主函数 - 股票简洁趋势线分析"""
    import sys
    
    # 从命令行参数获取股票代码和周期
    if len(sys.argv) > 1:
        stock_code = sys.argv[1]
    else:
        stock_code = "600521"  # 默认股票（华海药业，有明显上升趋势）
    
    # 从命令行参数获取周期
    if len(sys.argv) > 2:
        period = sys.argv[2]
        # 验证周期参数是否有效
        valid_periods = ["1年", "半年", "3个月", "1个月"]
        is_valid = period in valid_periods or (period.endswith('天') and period[:-1].isdigit() and int(period[:-1]) > 0)
        
        if not is_valid:
            print(f"❌ 无效的周期参数: {period}")
            print(f"📋 支持的周期格式:")
            print(f"  - 固定周期: {', '.join(valid_periods)}")
            print(f"  - 自定义天数: 30天, 60天, 90天, 120天等")
            return
    else:
        period = "60天"  # 默认周期（更适合趋势线分析）
    
    # 股票名称映射
    stock_names = {
        "600036": "招商银行",
        "000001": "平安银行", 
        "001328": "上海钢联",
        "601872": "招商轮船",
        "000002": "万科A",
        "600301": "南化股份",
        "600521": "华海药业",
        "000858": "五粮液",
        "600519": "贵州茅台",
        "000166": "申万宏源",
        "300015": "爱尔眼科",
        "002415": "海康威视",
        "600276": "恒瑞医药",
        "000568": "泸州老窖"
    }
    stock_name = stock_names.get(stock_code, f"股票{stock_code}")
    
    print(f"📈 {stock_name}({stock_code}) - {period}趋势线分析...")
    
    setup_matplotlib_chinese()
    
    print(f"📈 获取股票数据: {stock_code} ({period})")
    
    # 获取数据
    data_fetcher = StockDataFetcher()
    stock_data = data_fetcher.fetch_stock_data(stock_code, period)
    
    if stock_data.empty:
        print(f"❌ 无法获取股票 {stock_code} 的数据")
        return
    
    print(f"✅ 成功获取 {len(stock_data)} 个交易日的数据")
    print(f"数据范围: {stock_data['date'].min()} 到 {stock_data['date'].max()}")
    print(f"价格范围: {stock_data['low'].min():.2f} - {stock_data['high'].max():.2f}")
    
    # 计算趋势线（使用V5简化算法）
    print("\n📊 使用V5简化算法计算趋势线...")
    trend_calculator = TrendLineCalculatorV5()  # 使用默认容错率（支撑5%，阻力3%）
    
    trend_analysis = trend_calculator.calculate_trend_lines(stock_data)
    
    # 输出分析结果
    print(f"\n📋 趋势线分析结果:")
    print(f"  识别高点: {trend_analysis['total_highs']}个")
    print(f"  识别低点: {trend_analysis['total_lows']}个")
    print(f"  找到支撑线: {'是' if trend_analysis['support_line'] else '否'}")
    print(f"  找到阻力线: {'是' if trend_analysis['resistance_line'] else '否'}")
    
    # 绘制简洁图表
    print("\n🎨 绘制简洁K线图和趋势线...")
    output_path = plot_cmb_clean(stock_data, trend_analysis, stock_code, stock_name, period)
    
    # 输出详细信息
    print(f"\n📋 趋势线详细信息:")
    
    # 趋势一致性检查
    trend_consistency = check_trend_consistency(trend_analysis)
    print(f"\n📊 趋势一致性分析: {trend_consistency}")
    
    if trend_analysis['support_line']:
        line = trend_analysis['support_line']
        print(f"\n🟡 支撑线:")
        print(f"  强度: {line['strength_level']} - 评分:{line['quality_score']:.1f}/100")
        print(f"  方程: {line['equation']}")
        print(f"  趋势: {line['trend_direction']}")
        print(f"  状态: {line['current_status']}")
        print(f"  距离: {line['current_distance_percent']:.2f}%")
        print(f"  连接点: {len(line['connected_points'])}个")
    
    if trend_analysis['resistance_line']:
        line = trend_analysis['resistance_line']
        print(f"\n🔵 阻力线:")
        print(f"  强度: {line['strength_level']} - 评分:{line['quality_score']:.1f}/100")
        print(f"  方程: {line['equation']}")
        print(f"  趋势: {line['trend_direction']}")
        print(f"  状态: {line['current_status']}")
        print(f"  距离: {line['current_distance_percent']:.2f}%")
        print(f"  连接点: {len(line['connected_points'])}个")
    
    print(f"\n🎉 {stock_name}简洁趋势线分析完成！")
    print(f"图表保存位置: {output_path}")

if __name__ == "__main__":
    # 显示使用帮助
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print("📋 使用说明:")
        print("python test_cmb_clean.py [股票代码] [周期]")
        print("")
        print("参数:")
        print("  股票代码: 6位数字代码，如 600036, 000001, 001328")
        print("  周期: 1年(默认), 半年, 3个月, 1个月, 或自定义天数(如30天, 60天)")
        print("")
        print("示例:")
        print("  python test_cmb_clean.py                    # 默认600521，60天")
        print("  python test_cmb_clean.py 600036            # 招商银行，60天") 
        print("  python test_cmb_clean.py 600301 半年        # 南化股份，半年")
        print("  python test_cmb_clean.py 000001 3个月       # 平安银行，3个月")
        print("  python test_cmb_clean.py 000002 30天        # 万科A，30天")
        print("  python test_cmb_clean.py 600519 90天        # 贵州茅台，90天")
        print("")
        print("推荐股票代码:")
        print("  600521 华海药业 (上升趋势)  000002 万科A (下降趋势)")
        print("  600036 招商银行 (稳定)      000001 平安银行 (波动)")
        print("  600519 贵州茅台 (长期)      000858 五粮液 (消费)")
        exit(0)
    
    try:
        main()
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        import traceback
        traceback.print_exc() 